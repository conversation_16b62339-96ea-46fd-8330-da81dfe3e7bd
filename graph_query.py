"""
PTT 自動登入系統 - 圖形查詢封裝模組
封裝常用的圖形查詢操作
"""

import json
from typing import Dict, List, Optional, Any
from datetime import datetime
from gremlin_python.driver import client, serializer
from gremlin_python.process.graph_traversal import __
from gremlin_python.process.traversal import P
from gremlin_python.driver.protocol import GremlinServerError
from config import Config
from logger import ptt_logger

class GraphQuery:
    """圖形查詢器，負責執行各種圖形查詢操作"""
    
    def __init__(self):
        self.client = None
        self.is_connected = False
        self._init_connection()
    
    def _init_connection(self):
        """初始化 Cosmos DB Gremlin 連接"""
        try:
            # 從配置取得連接資訊
            endpoint = Config.COSMOS_DB_ENDPOINT
            key = Config.COSMOS_DB_KEY
            database = Config.COSMOS_DB_DATABASE
            collection = Config.COSMOS_DB_COLLECTION
            
            if not all([endpoint, key, database, collection]):
                ptt_logger.warning("Cosmos DB 配置不完整，圖形查詢功能將無法使用")
                return
            
            # 建立 Gremlin 客戶端
            try:
                # 嘗試使用 aiohttp 傳輸
                from gremlin_python.driver.aiohttp.transport import AiohttpTransport
                transport_factory = lambda: AiohttpTransport()

                # 確保端點格式正確
                if not endpoint.startswith('wss://'):
                    if endpoint.startswith('https://'):
                        endpoint = endpoint.replace('https://', 'wss://')
                    else:
                        endpoint = f'wss://{endpoint}'

                if not endpoint.endswith('/'):
                    endpoint += '/'

                self.client = client.Client(
                    endpoint,
                    'g',
                    username=f"/dbs/{database}/colls/{collection}",
                    password=key,
                    message_serializer=serializer.GraphSONSerializersV2d0(),
                    transport_factory=transport_factory
                )
            except ImportError:
                # 如果沒有 aiohttp，使用預設傳輸
                # 確保端點格式正確
                if not endpoint.startswith('wss://'):
                    if endpoint.startswith('https://'):
                        endpoint = endpoint.replace('https://', 'wss://')
                    else:
                        endpoint = f'wss://{endpoint}'

                if not endpoint.endswith('/'):
                    endpoint += '/'

                self.client = client.Client(
                    endpoint,
                    'g',
                    username=f"/dbs/{database}/colls/{collection}",
                    password=key,
                    message_serializer=serializer.GraphSONSerializersV2d0()
                )
            
            self.is_connected = True
            ptt_logger.info("圖形查詢器 Cosmos DB 連接初始化成功")
            
        except Exception as e:
            ptt_logger.error(f"初始化圖形查詢器 Cosmos DB 連接失敗: {e}")
            self.is_connected = False
    
    def _parse_username(self, username: str) -> tuple:
        """解析使用者名稱，分離 userid 和 nickname

        Args:
            username: 完整的使用者名稱，格式如 "userid (nickname)" 或 "userid"

        Returns:
            tuple: (userid, nickname)
        """
        import re

        # 使用正則表達式匹配 "userid (nickname)" 格式
        match = re.match(r'^([^(]+)\s*\(([^)]+)\)$', username.strip())

        if match:
            userid = match.group(1).strip()
            nickname = match.group(2).strip()
        else:
            # 如果沒有括號，整個字串就是 userid，nickname 為空
            userid = username.strip()
            nickname = ''

        return userid, nickname

    def query_user_links(self, username: str, max_depth: int = 3) -> Dict:
        """查詢使用者互動關聯

        Args:
            username: 使用者名稱 (可包含暱稱)
            max_depth: 查詢深度，預設2層

        Returns:
            dict: 使用者關聯資料 {
                'user': str,
                'connections': [
                    {
                        'type': 'posted_same_board' | 'commented_same_post' | 'used_same_ip',
                        'target_user': str,
                        'shared_items': [...]
                    }
                ],
                'posts': [...],
                'comments': [...],
                'ips': [...]
            }
        """
        if not self.is_connected:
            return {'error': '未連接到圖形資料庫'}

        try:
            # 解析使用者名稱，取得 userid
            userid, nickname = self._parse_username(username)

            ptt_logger.log_operation_start(f"查詢使用者關聯: {username}")

            result = {
                'user': username,
                'userid': userid,
                'nickname': nickname,
                'connections': [],
                'posts': [],
                'comments': [],
                'ips': []
            }
            
            # 查詢使用者發文
            posts_query = f"""
            g.V().hasLabel('user').has('userid', '{userid}')
            .outE('posted').inV().hasLabel('post')
            .project('post_id', 'title', 'board', 'date')
            .by('post_id').by('title').by('board').by('date')
            """

            posts_result = self.client.submit(posts_query).all().result()
            result['posts'] = posts_result

            # 查詢使用者推文
            comments_query = f"""
            g.V().hasLabel('user').has('userid', '{userid}')
            .outE('commented').as('comment')
            .inV().hasLabel('post').as('post')
            .select('comment', 'post')
            .by(valueMap('comment_type', 'time', 'content'))
            .by(valueMap('post_id', 'title', 'board'))
            """

            comments_result = self.client.submit(comments_query).all().result()
            result['comments'] = comments_result

            # 查詢使用者使用的 IP
            ips_query = f"""
            g.V().hasLabel('user').has('userid', '{userid}')
            .outE('used_ip').inV().hasLabel('ip')
            .values('address')
            """

            ips_result = self.client.submit(ips_query).all().result()
            result['ips'] = ips_result

            # 分層查詢關聯使用者
            connections = []

            # 第一層：直接關係查詢
            ptt_logger.info("查詢第一層關係...")
            layer1_connections = self._query_layer1_relationships(userid)
            connections.extend(layer1_connections)

            # 第二層：間接關係查詢
            if max_depth >= 2:
                ptt_logger.info("查詢第二層關係...")
                layer2_connections = self._query_layer2_relationships(userid)
                connections.extend(layer2_connections)

            # 第三層：更深層關係查詢
            if max_depth >= 3:
                ptt_logger.info("查詢第三層關係...")
                layer3_connections = self._query_layer3_relationships(userid, connections)
                connections.extend(layer3_connections)

            # 去重並整理連接
            result['connections'] = self._deduplicate_connections(connections)

            # 2. 查詢推文互動關聯 - 我推了誰的文章
            commented_on_query = f"""
            g.V().hasLabel('user').has('userid', '{userid}')
            .outE('commented').inV().hasLabel('post')
            .inE('posted').outV().hasLabel('user')
            .has('userid', neq('{userid}'))
            .dedup()
            .project('userid', 'nickname', 'posts')
            .by('userid').by('nickname')
            .by(
                outE('posted').inV().hasLabel('post')
                .where(inE('commented').outV().has('userid', '{userid}'))
                .values('title').fold()
            )
            """

            commented_on_users = self.client.submit(commented_on_query).all().result()

            for other_user in commented_on_users:
                target_userid = other_user.get('userid', '')
                target_nickname = other_user.get('nickname', '')
                target_display = f"{target_userid} ({target_nickname})" if target_nickname else target_userid
                shared_posts = other_user.get('posts', [])

                # 檢查是否已存在（可能因為IP關聯已經加入）
                existing_conn = next((c for c in result['connections'] if c.get('target_userid') == target_userid), None)
                if existing_conn:
                    existing_conn['type'] = 'multiple_relations'
                    existing_conn['role'] += ' + 我推他的文'
                    existing_conn['shared_items'].extend(shared_posts)
                else:
                    result['connections'].append({
                        'type': 'i_commented_their_post',
                        'target_user': target_display,
                        'target_userid': target_userid,
                        'target_nickname': target_nickname,
                        'shared_items': shared_posts,
                        'role': '我推他的文'
                    })

            # 3. 查詢推文互動關聯 - 誰推了我的文章
            commenters_query = f"""
            g.V().hasLabel('user').has('userid', '{userid}')
            .outE('posted').inV().hasLabel('post')
            .inE('commented').outV().hasLabel('user')
            .has('userid', neq('{userid}'))
            .dedup()
            .project('userid', 'nickname', 'posts')
            .by('userid').by('nickname')
            .by(
                outE('commented').inV().hasLabel('post')
                .where(inE('posted').outV().has('userid', '{userid}'))
                .values('title').fold()
            )
            """

            commenters = self.client.submit(commenters_query).all().result()

            for commenter in commenters:
                target_userid = commenter.get('userid', '')
                target_nickname = commenter.get('nickname', '')
                target_display = f"{target_userid} ({target_nickname})" if target_nickname else target_userid
                shared_posts = commenter.get('posts', [])

                # 檢查是否已存在
                existing_conn = next((c for c in result['connections'] if c.get('target_userid') == target_userid), None)
                if existing_conn:
                    existing_conn['type'] = 'multiple_relations'
                    existing_conn['role'] += ' + 他推我的文'
                    existing_conn['shared_items'].extend(shared_posts)
                else:
                    result['connections'].append({
                        'type': 'they_commented_my_post',
                        'target_user': target_display,
                        'target_userid': target_userid,
                        'target_nickname': target_nickname,
                        'shared_items': shared_posts,
                        'role': '他推我的文'
                    })

            # 4. 查詢同板發文關聯
            same_board_users_query = f"""
            g.V().hasLabel('user').has('userid', '{userid}').as('me')
            .outE('posted').inV().hasLabel('post').values('board').dedup().as('board')
            .V().hasLabel('post').has('board', select('board'))
            .inE('posted').outV().hasLabel('user')
            .has('userid', neq('{userid}'))
            .dedup()
            .project('userid', 'nickname', 'boards')
            .by('userid').by('nickname')
            .by(
                outE('posted').inV().hasLabel('post')
                .where(values('board').where(within(select('board'))))
                .values('board').dedup().fold()
            )
            """

            same_board_users = self.client.submit(same_board_users_query).all().result()

            for other_user in same_board_users:
                target_userid = other_user.get('userid', '')
                target_nickname = other_user.get('nickname', '')
                target_display = f"{target_userid} ({target_nickname})" if target_nickname else target_userid
                shared_boards = other_user.get('boards', [])

                # 檢查是否已存在
                existing_conn = next((c for c in result['connections'] if c.get('target_userid') == target_userid), None)
                if existing_conn:
                    existing_conn['type'] = 'multiple_relations'
                    existing_conn['role'] += ' + 同板發文'
                    if 'shared_boards' not in existing_conn:
                        existing_conn['shared_boards'] = shared_boards
                else:
                    result['connections'].append({
                        'type': 'posted_same_board',
                        'target_user': target_display,
                        'target_userid': target_userid,
                        'target_nickname': target_nickname,
                        'shared_items': shared_boards,
                        'shared_boards': shared_boards,
                        'role': '同板發文'
                    })
            
            ptt_logger.log_operation_end(f"查詢使用者關聯: {username}", True, 
                                       f"發文: {len(result['posts'])}, 推文: {len(result['comments'])}, "
                                       f"IP: {len(result['ips'])}, 關聯: {len(result['connections'])}")
            
            return result

        except Exception as e:
            ptt_logger.log_operation_end(f"查詢使用者關聯: {username}", False, f"錯誤: {e}")
            return {'error': str(e)}

    def _query_layer1_relationships(self, userid: str) -> List[Dict]:
        """查詢第一層關係：直接關聯

        Args:
            userid: 使用者ID

        Returns:
            list: 第一層關聯列表
        """
        connections = []

        try:
            # 1.1 查詢我推了誰的文章 - 使用當前資料庫格式
            ptt_logger.info("查詢第一層：我推了誰的文章")
            my_comments_query = f"""
            g.V().hasLabel('user').has('userid', '{userid}')
            .out('commented').hasLabel('post')
            .in('posted').hasLabel('user')
            .dedup()
            .valueMap('userid', 'nickname')
            """

            my_comments_result = self.client.submit(my_comments_query).all().result()

            # 計算互動詳情
            for user_data in my_comments_result:
                target_userid = self._extract_value(user_data.get('userid', ''))
                target_nickname = self._extract_value(user_data.get('nickname', ''))

                if target_userid and target_userid != userid:  # 排除自己
                    # 查詢具體的互動文章
                    interaction_details = self._get_comment_interaction_details(userid, target_userid)

                    connections.append({
                        'target_userid': target_userid,
                        'target_nickname': target_nickname,
                        'type': 'i_commented_their_post',
                        'role': '我推他的文',
                        'shared_items': interaction_details.get('posts', []),
                        'interaction_count': interaction_details.get('count', 0),
                        'latest_interaction': interaction_details.get('latest_time', ''),
                        'layer': 1,
                        'path': f"{userid} → 推文 → 文章 ← 發文 ← {target_userid}",
                        'strength': self._calculate_relationship_strength(interaction_details)
                    })

            # 1.2 查詢誰推了我的文章 (user → posted → post ← commented ← user)
            ptt_logger.info("查詢第一層：誰推了我的文章")
            my_posts_commenters_query = f"""
            g.V().hasLabel('user').has('userid', '{userid}')
            .out('posted').hasLabel('post')
            .in('commented').hasLabel('user')
            .dedup()
            .valueMap('userid', 'nickname')
            """

            my_posts_commenters_result = self.client.submit(my_posts_commenters_query).all().result()

            for user_data in my_posts_commenters_result:
                target_userid = self._extract_value(user_data.get('userid', ''))
                target_nickname = self._extract_value(user_data.get('nickname', ''))

                if target_userid and target_userid != userid:  # 排除自己
                    # 查詢具體的互動文章
                    interaction_details = self._get_comment_interaction_details(target_userid, userid)

                    connections.append({
                        'target_userid': target_userid,
                        'target_nickname': target_nickname,
                        'type': 'they_commented_my_post',
                        'role': '他推我的文',
                        'shared_items': interaction_details.get('posts', []),
                        'interaction_count': interaction_details.get('count', 0),
                        'latest_interaction': interaction_details.get('latest_time', ''),
                        'layer': 1,
                        'path': f"{userid} → 發文 → 文章 ← 推文 ← {target_userid}",
                        'strength': self._calculate_relationship_strength(interaction_details)
                    })

            # 1.3 查詢共用IP的使用者 (user → used_ip → ip ← used_ip ← user)
            ptt_logger.info("查詢第一層：共用IP的使用者")
            shared_ip_query = f"""
            g.V().hasLabel('user').has('userid', '{userid}')
            .out('used_ip').hasLabel('ip')
            .in('used_ip').hasLabel('user')
            .dedup()
            .valueMap('userid', 'nickname')
            """

            shared_ip_result = self.client.submit(shared_ip_query).all().result()

            for user_data in shared_ip_result:
                target_userid = self._extract_value(user_data.get('userid', ''))
                target_nickname = self._extract_value(user_data.get('nickname', ''))

                if target_userid and target_userid != userid:  # 排除自己
                    # 查詢共用IP詳情
                    ip_details = self._get_shared_ip_details(userid, target_userid)

                    connections.append({
                        'target_userid': target_userid,
                        'target_nickname': target_nickname,
                        'type': 'shared_ip',
                        'role': 'IP共用者',
                        'shared_items': ip_details.get('ips', []),
                        'interaction_count': ip_details.get('count', 0),
                        'latest_interaction': ip_details.get('latest_time', ''),
                        'layer': 1,
                        'path': f"{userid} → 使用IP → IP ← 使用IP ← {target_userid}",
                        'strength': self._calculate_ip_relationship_strength(ip_details)
                    })

        except Exception as e:
            ptt_logger.error(f"查詢第一層關係失敗: {e}")

        return connections

    def _query_layer2_relationships(self, userid: str) -> List[Dict]:
        """查詢第二層關係：通過中間節點的關聯

        Args:
            userid: 使用者ID

        Returns:
            list: 第二層關聯列表
        """
        connections = []

        try:
            # 2.1 查詢同板發文的使用者 - 簡化查詢
            ptt_logger.info("查詢第二層：同板發文的使用者")

            # 先取得該使用者發文的看板
            user_boards_query = f"""
            g.V().hasLabel('user').has('userid', '{userid}')
            .out('posted').hasLabel('post')
            .values('board').dedup()
            """

            user_boards_result = self.client.submit(user_boards_query).all().result()

            # 對每個看板查詢其他發文者
            board_users = {}  # 用於合併同一使用者在多個看板的關係

            for board in user_boards_result:
                same_board_users_query = f"""
                g.V().hasLabel('post').has('board', '{board}')
                .in('posted').hasLabel('user')
                .dedup()
                .valueMap('userid', 'nickname')
                """

                same_board_result = self.client.submit(same_board_users_query).all().result()

                for user_data in same_board_result:
                    target_userid = self._extract_value(user_data.get('userid', ''))
                    target_nickname = self._extract_value(user_data.get('nickname', ''))

                    if target_userid and target_userid != userid:  # 排除自己
                        if target_userid not in board_users:
                            board_users[target_userid] = {
                                'target_userid': target_userid,
                                'target_nickname': target_nickname,
                                'boards': [],
                                'post_counts': {}
                            }

                        board_users[target_userid]['boards'].append(board)

                        # 計算該使用者在此看板的發文數
                        post_count_query = f"""
                        g.V().hasLabel('user').has('userid', '{target_userid}')
                        .out('posted').hasLabel('post').has('board', '{board}')
                        .count()
                        """
                        post_count = self.client.submit(post_count_query).all().result()[0]
                        board_users[target_userid]['post_counts'][board] = post_count

            # 轉換為連接格式
            for target_userid, user_info in board_users.items():
                try:
                    board_details = self._get_board_interaction_details(userid, target_userid, user_info['boards'])

                    connections.append({
                        'target_userid': target_userid,
                        'target_nickname': user_info['target_nickname'],
                        'type': 'same_board_posting',
                        'role': f'同板發文({", ".join(user_info["boards"])})',
                        'shared_items': user_info['boards'],
                        'interaction_count': sum(user_info['post_counts'].values()),
                        'latest_interaction': board_details.get('latest_time', ''),
                        'layer': 2,
                        'path': f"{userid} → 發文 → 看板 ← 發文 ← {target_userid}",
                        'strength': self._calculate_board_relationship_strength(board_details, user_info['post_counts'])
                    })
                except Exception as e:
                    ptt_logger.error(f"處理看板關係 {target_userid} 失敗: {e}")
                    # 添加簡化版本
                    connections.append({
                        'target_userid': target_userid,
                        'target_nickname': user_info['target_nickname'],
                        'type': 'same_board_posting',
                        'role': f'同板發文({", ".join(user_info["boards"])})',
                        'shared_items': user_info['boards'],
                        'interaction_count': sum(user_info['post_counts'].values()),
                        'latest_interaction': '',
                        'layer': 2,
                        'path': f"{userid} → 發文 → 看板 ← 發文 ← {target_userid}",
                        'strength': 0.3  # 預設強度
                    })

        except Exception as e:
            ptt_logger.error(f"查詢第二層關係失敗: {e}")

        return connections

    def _query_layer3_relationships(self, userid: str, existing_connections: List[Dict]) -> List[Dict]:
        """查詢第三層關係：通過已知關聯使用者的延伸關係

        Args:
            userid: 使用者ID
            existing_connections: 已知的關聯列表

        Returns:
            list: 第三層關聯列表
        """
        connections = []

        try:
            # 取得第一、二層的關聯使用者
            known_users = set()
            for conn in existing_connections:
                if conn.get('target_userid'):
                    known_users.add(conn['target_userid'])

            # 限制查詢數量避免過度複雜
            for known_user in list(known_users)[:3]:
                ptt_logger.info(f"查詢第三層：通過 {known_user} 的延伸關係")

                # 3.1 簡化查詢：已知使用者推了誰的文
                mutual_targets_query = f"""
                g.V().hasLabel('user').has('userid', '{known_user}')
                .out('commented').hasLabel('post')
                .in('posted').hasLabel('user')
                .dedup()
                .valueMap('userid', 'nickname')
                """

                mutual_targets_result = self.client.submit(mutual_targets_query).all().result()
                for user_data in mutual_targets_result:
                    target_userid = self._extract_value(user_data.get('userid', ''))
                    target_nickname = self._extract_value(user_data.get('nickname', ''))

                    if target_userid and target_userid not in [userid, known_user]:
                        connections.append({
                            'target_userid': target_userid,
                            'target_nickname': target_nickname,
                            'type': 'mutual_comment_target',
                            'role': f'我和{known_user}都推他的文',
                            'shared_items': [known_user],
                            'layer': 3,
                            'path': f"{userid} → 推文 → 文章 ← 推文 ← {known_user} → 推文 → 文章 ← 發文 ← {target_userid}"
                        })

        except Exception as e:
            ptt_logger.error(f"查詢第三層關係失敗: {e}")

        return connections

    def _deduplicate_connections(self, connections: List[Dict]) -> List[Dict]:
        """去重並合併連接

        Args:
            connections: 原始連接列表

        Returns:
            list: 去重後的連接列表
        """
        user_connections = {}

        for conn in connections:
            target_userid = conn.get('target_userid', '')
            if not target_userid:
                continue

            if target_userid not in user_connections:
                # 建立基本連接資訊
                target_nickname = conn.get('target_nickname', '')
                target_display = f"{target_userid} ({target_nickname})" if target_nickname else target_userid

                user_connections[target_userid] = {
                    'target_user': target_display,
                    'target_userid': target_userid,
                    'target_nickname': target_nickname,
                    'types': [],
                    'roles': [],
                    'shared_items': [],
                    'layers': [],
                    'paths': []
                }

            # 合併資訊
            user_conn = user_connections[target_userid]
            user_conn['types'].append(conn.get('type', ''))
            user_conn['roles'].append(conn.get('role', ''))

            # 安全地處理 shared_items
            shared_items = conn.get('shared_items', [])
            if shared_items:
                # 確保所有項目都是可哈希的（字符串）
                for item in shared_items:
                    if isinstance(item, (str, int, float)):
                        user_conn['shared_items'].append(str(item))

            user_conn['layers'].append(conn.get('layer', 1))
            user_conn['paths'].append(conn.get('path', ''))

        # 轉換為最終格式
        final_connections = []
        for target_userid, conn_info in user_connections.items():
            # 決定主要類型和角色
            if len(conn_info['types']) == 1:
                main_type = conn_info['types'][0]
                main_role = conn_info['roles'][0]
            else:
                main_type = 'multiple_relations'
                main_role = ' + '.join(set(conn_info['roles']))

            # 安全地處理 shared_items 去重
            unique_shared_items = []
            seen_items = set()
            for item in conn_info['shared_items']:
                if isinstance(item, (str, int, float)):
                    item_str = str(item)
                    if item_str not in seen_items:
                        unique_shared_items.append(item_str)
                        seen_items.add(item_str)

            final_connections.append({
                'type': main_type,
                'target_user': conn_info['target_user'],
                'target_userid': target_userid,
                'target_nickname': conn_info['target_nickname'],
                'role': main_role,
                'shared_items': unique_shared_items,
                'layers': sorted(set(conn_info['layers'])),
                'paths': conn_info['paths'],
                'relationship_count': len(conn_info['types'])
            })

        return final_connections

    def _extract_value(self, value):
        """從Gremlin返回值中提取實際值"""
        if isinstance(value, list) and len(value) > 0:
            return value[0]
        return value if value is not None else ''

    def _get_comment_interaction_details(self, commenter_userid: str, author_userid: str) -> Dict:
        """取得推文互動詳情

        Args:
            commenter_userid: 推文者ID
            author_userid: 作者ID

        Returns:
            dict: 互動詳情
        """
        try:
            # 查詢推文互動的文章
            interaction_query = f"""
            g.V().hasLabel('user').has('userid', '{commenter_userid}')
            .outE('commented').as('comment')
            .inV().hasLabel('post')
            .where(inE('posted').outV().has('userid', '{author_userid}'))
            .project('title', 'board', 'post_id', 'comment_time', 'comment_type')
            .by('title').by('board').by('post_id')
            .by(select('comment').values('time'))
            .by(select('comment').values('comment_type'))
            """

            interactions = self.client.submit(interaction_query).all().result()

            posts = []
            latest_time = ''

            for interaction in interactions:
                post_info = {
                    'title': self._extract_value(interaction.get('title', '')),
                    'board': self._extract_value(interaction.get('board', '')),
                    'post_id': self._extract_value(interaction.get('post_id', '')),
                    'comment_time': self._extract_value(interaction.get('comment_time', '')),
                    'comment_type': self._extract_value(interaction.get('comment_type', ''))
                }
                posts.append(post_info)

                # 更新最新時間
                if post_info['comment_time'] and (not latest_time or post_info['comment_time'] > latest_time):
                    latest_time = post_info['comment_time']

            return {
                'posts': posts,
                'count': len(posts),
                'latest_time': latest_time
            }

        except Exception as e:
            ptt_logger.error(f"取得推文互動詳情失敗: {e}")
            return {'posts': [], 'count': 0, 'latest_time': ''}

    def _get_shared_ip_details(self, userid1: str, userid2: str) -> Dict:
        """取得共用IP詳情

        Args:
            userid1: 使用者1 ID
            userid2: 使用者2 ID

        Returns:
            dict: IP共用詳情
        """
        try:
            # 查詢共用的IP
            shared_ip_query = f"""
            g.V().hasLabel('user').has('userid', '{userid1}')
            .out('used_ip').hasLabel('ip').as('ip')
            .where(inE('used_ip').outV().has('userid', '{userid2}'))
            .project('address', 'first_seen', 'last_seen')
            .by('address').by('first_seen').by('last_seen')
            """

            shared_ips = self.client.submit(shared_ip_query).all().result()

            ips = []
            latest_time = ''

            for ip_info in shared_ips:
                ip_data = {
                    'address': self._extract_value(ip_info.get('address', '')),
                    'first_seen': self._extract_value(ip_info.get('first_seen', '')),
                    'last_seen': self._extract_value(ip_info.get('last_seen', ''))
                }
                ips.append(ip_data)

                # 更新最新時間
                if ip_data['last_seen'] and (not latest_time or ip_data['last_seen'] > latest_time):
                    latest_time = ip_data['last_seen']

            return {
                'ips': ips,
                'count': len(ips),
                'latest_time': latest_time
            }

        except Exception as e:
            ptt_logger.error(f"取得共用IP詳情失敗: {e}")
            return {'ips': [], 'count': 0, 'latest_time': ''}

    def _get_board_interaction_details(self, userid1: str, userid2: str, boards: List[str]) -> Dict:
        """取得看板互動詳情

        Args:
            userid1: 使用者1 ID
            userid2: 使用者2 ID
            boards: 共同看板列表

        Returns:
            dict: 看板互動詳情
        """
        try:
            latest_time = ''
            total_posts = 0

            for board in boards:
                # 查詢該看板的最新發文時間 - 簡化查詢
                latest_post_query = f"""
                g.V().hasLabel('post').has('board', '{board}')
                .where(inE('posted').outV().has('userid', within('{userid1}', '{userid2}')))
                .values('date').order().tail(1)
                """

                try:
                    board_latest = self.client.submit(latest_post_query).all().result()
                    if board_latest and board_latest[0]:
                        board_time = str(board_latest[0])
                        if not latest_time or board_time > latest_time:
                            latest_time = board_time
                except Exception as e:
                    ptt_logger.debug(f"查詢看板 {board} 最新時間失敗: {e}")
                    pass

                # 計算該看板的總發文數
                board_post_count_query = f"""
                g.V().hasLabel('post').has('board', '{board}')
                .where(inE('posted').outV().has('userid', within('{userid1}', '{userid2}')))
                .count()
                """

                try:
                    board_count = self.client.submit(board_post_count_query).all().result()
                    if board_count:
                        total_posts += board_count[0]
                except:
                    pass

            return {
                'boards': boards,
                'total_posts': total_posts,
                'latest_time': latest_time
            }

        except Exception as e:
            ptt_logger.error(f"取得看板互動詳情失敗: {e}")
            return {'boards': boards, 'total_posts': 0, 'latest_time': ''}

    def _calculate_relationship_strength(self, interaction_details: Dict) -> float:
        """計算推文關係強度

        Args:
            interaction_details: 互動詳情

        Returns:
            float: 關係強度 (0-1)
        """
        try:
            base_strength = 0.0

            # 互動次數權重 (0-0.4)
            interaction_count = interaction_details.get('count', 0)
            if interaction_count > 0:
                # 對數縮放，避免極值
                count_score = min(0.4, 0.1 + 0.3 * (interaction_count / (interaction_count + 5)))
                base_strength += count_score

            # 時間新鮮度權重 (0-0.3)
            latest_time = interaction_details.get('latest_time', '')
            if latest_time:
                try:
                    from datetime import datetime, timedelta
                    latest_dt = datetime.strptime(latest_time[:19], '%Y-%m-%d %H:%M:%S')
                    now = datetime.now()
                    days_ago = (now - latest_dt).days

                    # 30天內為滿分，之後遞減
                    if days_ago <= 30:
                        time_score = 0.3
                    elif days_ago <= 90:
                        time_score = 0.3 * (1 - (days_ago - 30) / 60)
                    elif days_ago <= 365:
                        time_score = 0.1 * (1 - (days_ago - 90) / 275)
                    else:
                        time_score = 0.05

                    base_strength += time_score
                except:
                    base_strength += 0.1  # 預設分數

            # 互動類型多樣性權重 (0-0.3)
            posts = interaction_details.get('posts', [])
            if posts:
                comment_types = set(post.get('comment_type', '') for post in posts)
                boards = set(post.get('board', '') for post in posts)

                # 推文類型多樣性
                type_diversity = min(0.15, len(comment_types) * 0.05)
                # 看板多樣性
                board_diversity = min(0.15, len(boards) * 0.03)

                base_strength += type_diversity + board_diversity

            return min(1.0, base_strength)

        except Exception as e:
            ptt_logger.error(f"計算關係強度失敗: {e}")
            return 0.1  # 預設最低強度

    def _calculate_ip_relationship_strength(self, ip_details: Dict) -> float:
        """計算IP關係強度

        Args:
            ip_details: IP詳情

        Returns:
            float: 關係強度 (0-1)
        """
        try:
            base_strength = 0.0

            # IP共用數量權重 (0-0.5)
            ip_count = ip_details.get('count', 0)
            if ip_count > 0:
                # IP共用是較強的關聯信號
                count_score = min(0.5, 0.2 + 0.3 * (ip_count / (ip_count + 3)))
                base_strength += count_score

            # 時間新鮮度權重 (0-0.3)
            latest_time = ip_details.get('latest_time', '')
            if latest_time:
                try:
                    from datetime import datetime
                    latest_dt = datetime.strptime(latest_time[:19], '%Y-%m-%d %H:%M:%S')
                    now = datetime.now()
                    days_ago = (now - latest_dt).days

                    # IP使用的時間新鮮度更重要
                    if days_ago <= 7:
                        time_score = 0.3
                    elif days_ago <= 30:
                        time_score = 0.25
                    elif days_ago <= 90:
                        time_score = 0.15
                    else:
                        time_score = 0.05

                    base_strength += time_score
                except:
                    base_strength += 0.1

            # IP類型權重 (0-0.2)
            ips = ip_details.get('ips', [])
            if ips:
                # 檢查是否為動態IP或固定IP
                dynamic_ips = sum(1 for ip in ips if self._is_dynamic_ip(ip.get('address', '')))
                if dynamic_ips < len(ips):  # 有固定IP
                    base_strength += 0.2
                else:  # 全是動態IP
                    base_strength += 0.1

            return min(1.0, base_strength)

        except Exception as e:
            ptt_logger.error(f"計算IP關係強度失敗: {e}")
            return 0.3  # IP關係預設較高強度

    def _calculate_board_relationship_strength(self, board_details: Dict, post_counts: Dict) -> float:
        """計算看板關係強度

        Args:
            board_details: 看板詳情
            post_counts: 發文數統計

        Returns:
            float: 關係強度 (0-1)
        """
        try:
            base_strength = 0.0

            # 共同看板數量權重 (0-0.3)
            board_count = len(board_details.get('boards', []))
            if board_count > 0:
                count_score = min(0.3, 0.1 + 0.2 * (board_count / (board_count + 3)))
                base_strength += count_score

            # 發文活躍度權重 (0-0.4)
            total_posts = sum(post_counts.values())
            if total_posts > 0:
                # 發文數量對看板關係很重要
                post_score = min(0.4, 0.1 + 0.3 * (total_posts / (total_posts + 10)))
                base_strength += post_score

            # 時間新鮮度權重 (0-0.3)
            latest_time = board_details.get('latest_time', '')
            if latest_time:
                try:
                    from datetime import datetime
                    latest_dt = datetime.strptime(latest_time[:19], '%Y-%m-%d %H:%M:%S')
                    now = datetime.now()
                    days_ago = (now - latest_dt).days

                    if days_ago <= 7:
                        time_score = 0.3
                    elif days_ago <= 30:
                        time_score = 0.2
                    elif days_ago <= 90:
                        time_score = 0.1
                    else:
                        time_score = 0.05

                    base_strength += time_score
                except:
                    base_strength += 0.05

            return min(1.0, base_strength)

        except Exception as e:
            ptt_logger.error(f"計算看板關係強度失敗: {e}")
            return 0.2  # 看板關係預設中等強度

    def _is_dynamic_ip(self, ip_address: str) -> bool:
        """判斷是否為動態IP

        Args:
            ip_address: IP位址

        Returns:
            bool: 是否為動態IP
        """
        # 簡單的動態IP判斷邏輯
        # 可以根據實際需求擴展
        dynamic_patterns = [
            '60.', '61.', '114.', '123.',  # 常見的台灣動態IP段
            '1.', '27.', '36.', '39.',
            '42.', '49.', '59.', '101.',
            '106.', '110.', '111.', '112.',
            '118.', '119.', '120.', '121.',
            '122.', '124.', '125.', '163.',
            '175.', '180.', '182.', '183.',
            '211.', '218.', '220.', '221.'
        ]

        return any(ip_address.startswith(pattern) for pattern in dynamic_patterns)

    def query_ip_users(self, ip_address: str) -> Dict:
        """查詢使用過該 IP 的帳號
        
        Args:
            ip_address: IP 位址
            
        Returns:
            dict: IP 使用者資料 {
                'ip': str,
                'users': [
                    {
                        'username': str,
                        'first_seen': str,
                        'last_seen': str,
                        'post_count': int,
                        'comment_count': int
                    }
                ],
                'posts_from_ip': [...],
                'timeline': [...]
            }
        """
        if not self.is_connected:
            return {'error': '未連接到圖形資料庫'}
        
        try:
            ptt_logger.log_operation_start(f"查詢 IP 使用者: {ip_address}")
            
            result = {
                'ip': ip_address,
                'users': [],
                'posts_from_ip': [],
                'timeline': []
            }
            
            # 查詢使用該 IP 的使用者
            users_query = f"""
            g.V().hasLabel('ip').has('address', '{ip_address}')
            .inE('used_ip').outV().hasLabel('user')
            .project('userid', 'nickname', 'first_seen', 'last_seen')
            .by('userid')
            .by('nickname')
            .by('first_seen')
            .by('last_seen')
            """
            
            users_result = self.client.submit(users_query).all().result()
            result['users'] = users_result
            
            # 查詢來自該 IP 的文章
            posts_query = f"""
            g.V().hasLabel('ip').has('address', '{ip_address}')
            .inE('from_ip').outV().hasLabel('post')
            .project('post_id', 'title', 'board', 'date')
            .by('post_id').by('title').by('board').by('date')
            """
            
            posts_result = self.client.submit(posts_query).all().result()
            result['posts_from_ip'] = posts_result
            
            ptt_logger.log_operation_end(f"查詢 IP 使用者: {ip_address}", True, 
                                       f"使用者: {len(result['users'])}, 文章: {len(result['posts_from_ip'])}")
            
            return result
            
        except Exception as e:
            ptt_logger.log_operation_end(f"查詢 IP 使用者: {ip_address}", False, f"錯誤: {e}")
            return {'error': str(e)}
    
    def query_post_interactions(self, post_id: str) -> Dict:
        """查詢該文互動網絡
        
        Args:
            post_id: 文章 ID
            
        Returns:
            dict: 文章互動資料 {
                'post': {...},
                'author': str,
                'commenters': [
                    {
                        'username': str,
                        'comment_type': str,
                        'content': str,
                        'time': str
                    }
                ],
                'interaction_network': {
                    'nodes': [...],
                    'edges': [...]
                }
            }
        """
        if not self.is_connected:
            return {'error': '未連接到圖形資料庫'}
        
        try:
            ptt_logger.log_operation_start(f"查詢文章互動: {post_id}")
            
            result = {
                'post': {},
                'author': '',
                'commenters': [],
                'interaction_network': {
                    'nodes': [],
                    'edges': []
                }
            }
            
            # 查詢文章基本資訊
            post_query = f"""
            g.V().hasLabel('post').has('post_id', '{post_id}')
            .project('post_id', 'title', 'board', 'date', 'content_length')
            .by('post_id').by('title').by('board').by('date').by('content_length')
            """
            
            post_result = self.client.submit(post_query).all().result()
            if post_result:
                result['post'] = post_result[0]
            
            # 查詢文章作者
            author_query = f"""
            g.V().hasLabel('post').has('post_id', '{post_id}')
            .inE('posted').outV().hasLabel('user')
            .values('username')
            """
            
            author_result = self.client.submit(author_query).all().result()
            if author_result:
                result['author'] = author_result[0]
            
            # 查詢推文者
            commenters_query = f"""
            g.V().hasLabel('post').has('post_id', '{post_id}')
            .inE('commented').as('comment')
            .outV().hasLabel('user').as('user')
            .select('user', 'comment')
            .by('username')
            .by(valueMap('comment_type', 'time', 'content'))
            """
            
            commenters_result = self.client.submit(commenters_query).all().result()
            result['commenters'] = commenters_result
            
            # 建立互動網絡
            nodes = []
            edges = []
            
            # 文章節點
            nodes.append({
                'id': post_id,
                'type': 'post',
                'label': result['post'].get('title', post_id)
            })
            
            # 作者節點和邊
            if result['author']:
                nodes.append({
                    'id': result['author'],
                    'type': 'user',
                    'label': result['author']
                })
                edges.append({
                    'from': result['author'],
                    'to': post_id,
                    'type': 'posted'
                })
            
            # 推文者節點和邊
            for commenter_data in result['commenters']:
                username = commenter_data.get('user', '')
                if username and username not in [node['id'] for node in nodes]:
                    nodes.append({
                        'id': username,
                        'type': 'user',
                        'label': username
                    })
                
                if username:
                    edges.append({
                        'from': username,
                        'to': post_id,
                        'type': 'commented'
                    })
            
            result['interaction_network'] = {
                'nodes': nodes,
                'edges': edges
            }
            
            ptt_logger.log_operation_end(f"查詢文章互動: {post_id}", True, 
                                       f"推文者: {len(result['commenters'])}, 網絡節點: {len(nodes)}")
            
            return result
            
        except Exception as e:
            ptt_logger.log_operation_end(f"查詢文章互動: {post_id}", False, f"錯誤: {e}")
            return {'error': str(e)}
    
    def search_users_by_pattern(self, pattern: str, limit: int = 20) -> List[Dict]:
        """根據模式搜尋使用者
        
        Args:
            pattern: 搜尋模式
            limit: 結果限制
            
        Returns:
            list: 使用者列表
        """
        if not self.is_connected:
            return []
        
        try:
            query = f"""
            g.V().hasLabel('user')
            .has('username', containing('{pattern}'))
            .limit({limit})
            .project('username', 'first_seen', 'last_seen')
            .by('username').by('first_seen').by('last_seen')
            """
            
            result = self.client.submit(query).all().result()
            return result
            
        except Exception as e:
            ptt_logger.error(f"搜尋使用者失敗: {e}")
            return []
    
    def get_popular_boards(self, limit: int = 10) -> List[Dict]:
        """取得熱門看板
        
        Args:
            limit: 結果限制
            
        Returns:
            list: 看板列表
        """
        if not self.is_connected:
            return []
        
        try:
            query = f"""
            g.V().hasLabel('post')
            .groupCount().by('board')
            .order(local).by(values, desc)
            .limit(local, {limit})
            """
            
            result = self.client.submit(query).all().result()
            
            # 轉換為更友善的格式
            boards = []
            if result:
                for board, count in result[0].items():
                    boards.append({
                        'board': board,
                        'post_count': count
                    })
            
            return boards

        except Exception as e:
            ptt_logger.error(f"取得熱門看板失敗: {e}")
            return []

    def get_all_users(self, limit: int = 100, offset: int = 0) -> List[Dict]:
        """取得所有使用者列表

        Args:
            limit: 結果限制
            offset: 偏移量

        Returns:
            list: 使用者列表
        """
        if not self.is_connected:
            return []

        try:
            # 簡化查詢，先確保基本功能正常
            query = f"""
            g.V().hasLabel('user')
            .range({offset}, {offset + limit})
            .valueMap('userid', 'nickname', 'first_seen', 'last_seen')
            """

            result = self.client.submit(query).all().result()

            # 轉換為更友善的格式
            users = []
            for user_data in result:
                user = {}
                for key, value in user_data.items():
                    # Gremlin 返回的值通常是列表，取第一個元素
                    if isinstance(value, list) and len(value) > 0:
                        user[key] = value[0]
                    else:
                        user[key] = value
                users.append(user)

            return users

        except Exception as e:
            ptt_logger.error(f"取得所有使用者失敗: {e}")
            return []

    def get_all_ips(self, limit: int = 100, offset: int = 0) -> List[Dict]:
        """取得所有 IP 位址列表

        Args:
            limit: 結果限制
            offset: 偏移量

        Returns:
            list: IP 位址列表
        """
        if not self.is_connected:
            return []

        try:
            # 簡化查詢
            query = f"""
            g.V().hasLabel('ip')
            .range({offset}, {offset + limit})
            .valueMap('address', 'first_seen', 'last_seen')
            """

            result = self.client.submit(query).all().result()

            # 轉換為更友善的格式
            ips = []
            for ip_data in result:
                ip = {}
                for key, value in ip_data.items():
                    # Gremlin 返回的值通常是列表，取第一個元素
                    if isinstance(value, list) and len(value) > 0:
                        ip[key] = value[0]
                    else:
                        ip[key] = value
                ips.append(ip)

            return ips

        except Exception as e:
            ptt_logger.error(f"取得所有 IP 失敗: {e}")
            return []

    def get_all_posts(self, limit: int = 50, offset: int = 0, board: str = None) -> Dict:
        """取得所有文章列表及其發文者和推文者資訊

        Args:
            limit: 結果限制
            offset: 偏移量
            board: 看板篩選 (可選)

        Returns:
            dict: 文章列表資料 {
                'posts': [
                    {
                        'post_id': str,
                        'title': str,
                        'board': str,
                        'date': str,
                        'content_length': int,
                        'author': {
                            'userid': str,
                            'nickname': str,
                            'ip': str  # 發文時的IP
                        },
                        'commenters': [
                            {
                                'userid': str,
                                'nickname': str,
                                'comment_type': str,
                                'time': str,
                                'content': str,
                                'ip': str  # 推文時的IP (如果有)
                            }
                        ],
                        'stats': {
                            'total_comments': int,
                            'unique_commenters': int,
                            'push_count': int,
                            'boo_count': int
                        }
                    }
                ],
                'total_count': int,
                'has_more': bool
            }
        """
        if not self.is_connected:
            return {'posts': [], 'total_count': 0, 'has_more': False}

        try:
            ptt_logger.log_operation_start(f"查詢文章列表: limit={limit}, offset={offset}, board={board}")

            # 建立基本查詢條件
            board_filter = f".has('board', '{board}')" if board else ""

            # 查詢文章基本資訊 (簡化查詢)
            posts_query = f"""
            g.V().hasLabel('post'){board_filter}
            .range({offset}, {offset + limit})
            .project('post_id', 'title', 'board', 'date', 'content_length')
            .by('post_id').by('title').by('board').by('date').by('content_length')
            """

            posts_result = self.client.submit(posts_query).all().result()

            # 查詢總數
            count_query = f"""
            g.V().hasLabel('post'){board_filter}.count()
            """
            total_count = self.client.submit(count_query).all().result()[0] if self.client.submit(count_query).all().result() else 0

            posts_data = []

            for post in posts_result:
                post_id = post.get('post_id', '')

                # 查詢文章作者及發文IP (簡化查詢)
                author_query = f"""
                g.V().hasLabel('post').has('post_id', '{post_id}')
                .in('posted').hasLabel('user')
                .valueMap('userid', 'nickname')
                """

                author_result = self.client.submit(author_query).all().result()
                author_info = {'userid': '', 'nickname': '', 'ip': ''}

                if author_result:
                    author_map = author_result[0]
                    author_info['userid'] = self._extract_value(author_map.get('userid', ''))
                    author_info['nickname'] = self._extract_value(author_map.get('nickname', ''))

                # 查詢發文IP
                ip_query = f"""
                g.V().hasLabel('post').has('post_id', '{post_id}')
                .inE('posted').values('ip')
                """

                ip_result = self.client.submit(ip_query).all().result()
                if ip_result:
                    author_info['ip'] = ip_result[0] if ip_result[0] else ''

                # 查詢推文者資訊
                commenters_query = f"""
                g.V().hasLabel('post').has('post_id', '{post_id}')
                .inE('commented').as('comment_edge')
                .outV().hasLabel('user').as('commenter')
                .select('commenter', 'comment_edge')
                .by(valueMap('userid', 'nickname'))
                .by(valueMap('comment_type', 'time', 'content', 'ip'))
                """

                commenters_result = self.client.submit(commenters_query).all().result()
                commenters_data = []

                for commenter_data in commenters_result:
                    commenter_info = {
                        'userid': '',
                        'nickname': '',
                        'comment_type': '',
                        'time': '',
                        'content': '',
                        'ip': ''
                    }

                    if 'commenter' in commenter_data:
                        commenter_map = commenter_data['commenter']
                        commenter_info['userid'] = self._extract_value(commenter_map.get('userid', ''))
                        commenter_info['nickname'] = self._extract_value(commenter_map.get('nickname', ''))

                    if 'comment_edge' in commenter_data:
                        comment_edge = commenter_data['comment_edge']
                        commenter_info['comment_type'] = self._extract_value(comment_edge.get('comment_type', ''))
                        commenter_info['time'] = self._extract_value(comment_edge.get('time', ''))
                        commenter_info['content'] = self._extract_value(comment_edge.get('content', ''))
                        commenter_info['ip'] = self._extract_value(comment_edge.get('ip', ''))

                    commenters_data.append(commenter_info)

                # 計算統計資訊
                total_comments = len(commenters_data)
                unique_commenters = len(set(c['userid'] for c in commenters_data if c['userid']))
                push_count = len([c for c in commenters_data if c['comment_type'] == '推'])
                boo_count = len([c for c in commenters_data if c['comment_type'] == '噓'])

                post_data = {
                    'post_id': post_id,
                    'title': post.get('title', ''),
                    'board': post.get('board', ''),
                    'date': post.get('date', ''),
                    'content_length': post.get('content_length', 0),
                    'author': author_info,
                    'commenters': commenters_data,
                    'stats': {
                        'total_comments': total_comments,
                        'unique_commenters': unique_commenters,
                        'push_count': push_count,
                        'boo_count': boo_count
                    }
                }

                posts_data.append(post_data)

            result = {
                'posts': posts_data,
                'total_count': total_count,
                'has_more': (offset + limit) < total_count
            }

            ptt_logger.log_operation_end(f"查詢文章列表", True,
                                       f"找到 {len(posts_data)} 篇文章，總計 {total_count} 篇")

            return result

        except Exception as e:
            ptt_logger.log_operation_end(f"查詢文章列表", False, f"錯誤: {e}")
            return {'posts': [], 'total_count': 0, 'has_more': False, 'error': str(e)}

    def _extract_value(self, value):
        """從Gremlin返回值中提取實際值"""
        if isinstance(value, list) and len(value) > 0:
            return value[0]
        return value if value is not None else ''

    def analyze_user_ip_relationships(self, username: str) -> Dict:
        """分析使用者的 IP 使用情況和相同 IP 的其他使用者

        Args:
            username: 使用者名稱 (可包含暱稱)

        Returns:
            dict: 分析結果
        """
        if not self.is_connected:
            return {'error': '未連接到資料庫'}

        try:
            # 解析使用者名稱，取得 userid
            userid, nickname = self._parse_username(username)

            ptt_logger.log_operation_start(f"分析使用者 IP 關係: {username}")

            result = {
                'username': username,
                'userid': userid,
                'nickname': nickname,
                'user_ips': [],
                'ip_analysis': [],
                'related_users': [],
                'summary': {}
            }

            # 1. 查詢使用者使用的所有 IP
            user_ips_query = f"""
            g.V().hasLabel('user').has('userid', '{userid}')
            .outE('used_ip')
            .project('ip', 'date', 'action')
            .by(inV().values('address'))
            .by('date')
            .by('action')
            """

            user_ips_result = self.client.submit(user_ips_query).all().result()
            result['user_ips'] = user_ips_result

            # 2. 對每個 IP 分析其他使用者
            unique_ips = list(set([ip_data.get('ip', '') for ip_data in user_ips_result]))

            for ip_address in unique_ips:
                if not ip_address:
                    continue

                # 查詢使用相同 IP 的其他使用者
                ip_users_query = f"""
                g.V().hasLabel('ip').has('address', '{ip_address}')
                .inE('used_ip')
                .project('userid', 'nickname', 'date', 'action')
                .by(outV().values('userid'))
                .by(outV().values('nickname'))
                .by('date')
                .by('action')
                """

                ip_users_result = self.client.submit(ip_users_query).all().result()

                # 查詢從此 IP 發出的文章
                ip_posts_query = f"""
                g.V().hasLabel('ip').has('address', '{ip_address}')
                .inE('from_ip')
                .project('post_id', 'title', 'author_userid', 'author_nickname', 'date')
                .by(outV().values('post_id'))
                .by(outV().values('title'))
                .by(outV().inE('posted').outV().values('userid'))
                .by(outV().inE('posted').outV().values('nickname'))
                .by('date')
                """

                ip_posts_result = self.client.submit(ip_posts_query).all().result()

                # 統計此 IP 的使用情況
                other_users = [u.get('userid', '') for u in ip_users_result
                              if u.get('userid', '') != userid]

                ip_analysis = {
                    'ip_address': ip_address,
                    'total_users': len(ip_users_result),
                    'other_users': list(set(other_users)),
                    'other_users_count': len(set(other_users)),
                    'posts_from_ip': ip_posts_result,
                    'posts_count': len(ip_posts_result),
                    'all_users_detail': ip_users_result
                }

                result['ip_analysis'].append(ip_analysis)

                # 收集相關使用者
                for other_user_id in set(other_users):
                    if not other_user_id:
                        continue

                    # 找到對應的使用者詳細資訊
                    other_user_detail = next((u for u in ip_users_result if u.get('userid') == other_user_id), {})
                    other_nickname = other_user_detail.get('nickname', '')
                    other_display = f"{other_user_id} ({other_nickname})" if other_nickname else other_user_id

                    if other_user_id not in [u['userid'] for u in result['related_users']]:
                        result['related_users'].append({
                            'userid': other_user_id,
                            'nickname': other_nickname,
                            'username': other_display,
                            'shared_ips': [ip_address],
                            'relationship_strength': 1
                        })
                    else:
                        # 更新現有使用者的共享 IP
                        for user_data in result['related_users']:
                            if user_data['userid'] == other_user_id:
                                user_data['shared_ips'].append(ip_address)
                                user_data['relationship_strength'] += 1
                                break

            # 3. 生成摘要
            total_ips = len(unique_ips)
            total_related_users = len(result['related_users'])
            suspicious_ips = len([ip for ip in result['ip_analysis']
                                if ip['other_users_count'] > 0])

            result['summary'] = {
                'total_ips_used': total_ips,
                'total_related_users': total_related_users,
                'suspicious_ips': suspicious_ips,
                'risk_level': 'high' if suspicious_ips > 2 else 'medium' if suspicious_ips > 0 else 'low',
                'analysis_timestamp': datetime.now().isoformat()
            }

            ptt_logger.log_operation_end(f"分析使用者 IP 關係: {username}", True,
                                       f"IP: {total_ips}, 相關使用者: {total_related_users}")

            return result

        except Exception as e:
            ptt_logger.log_operation_end(f"分析使用者 IP 關係: {username}", False, f"錯誤: {e}")
            return {'error': str(e)}

    def close(self):
        """關閉資料庫連接"""
        if self.client:
            try:
                self.client.close()
                self.is_connected = False
                ptt_logger.info("圖形查詢器 Cosmos DB 連接已關閉")
            except Exception as e:
                ptt_logger.warning(f"關閉圖形查詢器 Cosmos DB 連接時發生錯誤: {e}")
    
    def __del__(self):
        """析構函數，確保連接被關閉"""
        self.close()
