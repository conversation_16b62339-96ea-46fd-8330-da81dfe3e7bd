# PTT 自動登入系統 - 使用指南

## 快速開始

### 1. 設定環境變數

建立 `.env` 檔案並設定您的 PTT 帳號資訊：

```bash
# 複製範例檔案
cp .env.example .env
```

編輯 `.env` 檔案：

```env
PTT_USERNAME=your_ptt_username
PTT_PASSWORD=your_ptt_password
```

### 2. 啟動系統

```bash
# 安裝依賴套件
py -m pip install -r requirements.txt

# 執行系統測試
py test_system.py

# 啟動Web應用程式
py app.py
```

### 3. 開啟測試介面

在瀏覽器中開啟：http://127.0.0.1:5000

## Web介面功能

### 主要功能

1. **系統配置顯示**
   - 查看當前的系統配置
   - 敏感資訊會自動隱藏（如密碼）
   - 顯示連接參數和重試設定

2. **PTT 登入測試**
   - 可以使用環境變數中的帳密
   - 也可以在表單中臨時輸入帳密進行測試
   - 即時顯示測試進度和結果

3. **詳細測試結果**
   - 顯示連接過程的每個步驟
   - 成功/失敗狀態清楚標示
   - 包含時間戳記和詳細錯誤訊息

4. **系統狀態監控**
   - PTT 客戶端連接狀態
   - 配置驗證結果
   - 最後操作時間

### 測試流程

1. **連接測試**：嘗試連接到 PTT 伺服器
2. **登入測試**：使用提供的帳密進行登入
3. **使用者資訊**：取得登入後的使用者資訊
4. **清理連接**：自動登出並斷開連接

## API 端點

### GET /
- **功能**：主頁面
- **回應**：HTML 測試介面

### POST /test-login
- **功能**：執行 PTT 登入測試
- **參數**：
  - `username` (可選)：PTT 帳號
  - `password` (可選)：PTT 密碼
- **回應**：JSON 格式的測試結果

### GET /status
- **功能**：取得系統狀態
- **回應**：JSON 格式的狀態資訊

### GET /health
- **功能**：健康檢查
- **回應**：JSON 格式的健康狀態

## 測試結果說明

### 成功案例
```json
{
  "success": true,
  "message": "PTT 登入測試成功",
  "details": [
    "正在連接 PTT...",
    "✓ PTT 連接成功",
    "正在嘗試登入...",
    "✓ PTT 登入成功",
    "✓ 使用者資訊: your_username"
  ],
  "timestamp": "2025-06-16 14:30:00"
}
```

### 失敗案例
```json
{
  "success": false,
  "message": "PTT 登入失敗",
  "details": [
    "正在連接 PTT...",
    "✓ PTT 連接成功",
    "正在嘗試登入...",
    "✗ 錯誤: 帳號或密碼錯誤"
  ],
  "timestamp": "2025-06-16 14:30:00"
}
```

## 常見問題

### Q: 為什麼連接失敗？
A: 請檢查：
- 網路連接是否正常
- PTT 伺服器是否可用
- 防火牆設定是否阻擋連接

### Q: 為什麼登入失敗？
A: 請檢查：
- 帳號密碼是否正確
- 是否有其他 PTT 連線正在使用
- 帳號是否被暫時鎖定

### Q: 如何查看詳細的錯誤訊息？
A: 查看以下位置：
- Web 介面的測試結果區域
- 控制台輸出
- `logs/` 目錄下的日誌檔案

### Q: 可以同時測試多個帳號嗎？
A: 目前系統設計為單一帳號測試，但您可以：
- 在表單中輸入不同的帳密進行測試
- 修改 `.env` 檔案後重新啟動系統

## 安全注意事項

1. **環境變數保護**
   - `.env` 檔案已加入 `.gitignore`
   - 請勿將帳密提交到版本控制系統

2. **密碼安全**
   - Web 介面會隱藏密碼顯示
   - 日誌檔案不會記錄完整密碼

3. **連接安全**
   - 系統會自動清理連接
   - 避免長時間保持 PTT 連線

## 下一步

成功測試登入後，您可以：

1. **擴展功能**：
   - 添加自動發文功能
   - 實作排程任務
   - 增加多帳號支援

2. **部署到雲端**：
   - 使用 Azure App Service
   - 設定環境變數
   - 配置監控和警報

3. **增強安全性**：
   - 使用 Azure Key Vault
   - 實作 OAuth 認證
   - 添加 API 金鑰保護

## 技術支援

如果遇到問題，請：

1. 查看 `logs/` 目錄下的日誌檔案
2. 執行 `py test_system.py` 進行系統診斷
3. 檢查 GitHub Issues 或建立新的 Issue
