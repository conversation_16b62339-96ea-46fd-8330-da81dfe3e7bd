# Azure Web App 部署指南

## 部署準備

### 1. 環境變數設定

在 Azure Web App 的「組態」→「應用程式設定」中添加以下環境變數：

#### PTT 設定
```
PTT_USERNAME=your_ptt_username
PTT_PASSWORD=your_ptt_password
PTT_HOST=ptt.cc
PTT_PORT=23
```

#### Azure Cosmos DB 設定
```
COSMOS_DB_ENDPOINT=wss://your-cosmos-account.gremlin.cosmos.azure.com:443/
COSMOS_DB_KEY=your_cosmos_db_primary_key
COSMOS_DB_DATABASE=ptt_graph_db
COSMOS_DB_COLLECTION=ptt_graph
```

#### 系統設定
```
LOG_LEVEL=INFO
TIMEZONE=Asia/Taipei
POST_SCHEDULE=08:00
SECRET_KEY=your-production-secret-key-here
FLASK_DEBUG=False
MAX_RETRY_ATTEMPTS=3
RETRY_DELAY=5
CONNECTION_TIMEOUT=30
LOGIN_TIMEOUT=10
```

#### 爬蟲設定
```
DEFAULT_BOARDS=Test,Gossiping
MAX_POSTS_PER_BOARD=50
CRAWL_INTERVAL_HOURS=24
```

### 2. Python 版本設定

在 Azure Web App 的「組態」→「一般設定」中：
- Python 版本：3.13
- 啟動命令：`gunicorn --bind=0.0.0.0 --timeout 600 app:app`

### 3. 部署方式

#### 方式一：GitHub Actions（推薦）
1. 將程式碼推送到 GitHub
2. 在 Azure Web App 中設定「部署中心」
3. 選擇 GitHub 作為來源
4. 設定自動部署

#### 方式二：ZIP 部署
1. 將專案打包為 ZIP 檔案
2. 使用 Azure CLI 或入口網站上傳

#### 方式三：Git 部署
1. 設定本地 Git 部署
2. 推送到 Azure Git 倉庫

## 部署後檢查

### 1. 健康檢查
訪問：`https://twlotterybot.azurewebsites.net/api/system/health`

### 2. 系統狀態
訪問：`https://twlotterybot.azurewebsites.net/api/system/status`

### 3. 功能測試
- 使用者分析：`/user-analysis`
- IP 分析：`/ip-analysis`
- 文章互動：`/post-analysis`
- 系統狀態：`/system-status`
- 爬文管理：`/crawl-management`

## 常見問題

### 1. Cosmos DB 連接失敗
- 檢查 COSMOS_DB_ENDPOINT 和 COSMOS_DB_KEY 是否正確
- 確認 Cosmos DB 防火牆設定允許 Azure 服務存取

### 2. PTT 連接問題
- 檢查 PTT_USERNAME 和 PTT_PASSWORD
- 確認網路連接正常

### 3. 記憶體不足
- 調整 Azure Web App 的定價層
- 優化程式碼記憶體使用

### 4. 逾時問題
- 增加 CONNECTION_TIMEOUT 和 LOGIN_TIMEOUT
- 使用非同步處理長時間操作

## 監控與日誌

### 1. Application Insights
- 啟用 Application Insights 監控
- 設定自訂遙測

### 2. 日誌檢視
- 在 Azure 入口網站查看「日誌串流」
- 使用 Kudu 主控台檢查檔案

### 3. 效能監控
- 監控 CPU 和記憶體使用率
- 設定警示規則

## 安全性考量

### 1. 環境變數
- 不要在程式碼中硬編碼敏感資訊
- 使用 Azure Key Vault 存儲機密

### 2. 網路安全
- 設定適當的 CORS 政策
- 考慮使用 Azure Front Door

### 3. 存取控制
- 設定適當的驗證機制
- 限制 API 存取權限

## 擴展性

### 1. 水平擴展
- 設定自動縮放規則
- 使用多個執行個體

### 2. 快取
- 實作 Redis 快取
- 優化資料庫查詢

### 3. CDN
- 使用 Azure CDN 加速靜態資源
- 優化圖片和 CSS/JS 檔案
