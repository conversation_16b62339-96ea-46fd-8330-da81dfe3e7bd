<template>
  <div class="network-graph-container">
    <!-- 控制面板 -->
    <div class="graph-controls mb-3">
      <div class="row">
        <div class="col-md-6">
          <div class="btn-group" role="group">
            <button 
              type="button" 
              class="btn btn-outline-primary btn-sm"
              @click="resetZoom"
            >
              <i class="bi bi-zoom-out"></i> 重置視圖
            </button>
            <button 
              type="button" 
              class="btn btn-outline-primary btn-sm"
              @click="toggleLayout"
            >
              <i class="bi bi-diagram-3"></i> 切換佈局
            </button>
            <button 
              type="button" 
              class="btn btn-outline-primary btn-sm"
              @click="exportGraph"
            >
              <i class="bi bi-download"></i> 匯出圖片
            </button>
          </div>
        </div>
        <div class="col-md-6">
          <div class="d-flex align-items-center justify-content-end">
            <label class="form-label me-2 mb-0">節點大小:</label>
            <input 
              type="range" 
              class="form-range" 
              min="10" 
              max="50" 
              v-model="nodeSize"
              style="width: 100px;"
            >
          </div>
        </div>
      </div>
    </div>

    <!-- 圖形容器 -->
    <div 
      ref="graphContainer" 
      class="graph-container border rounded"
      :style="{ height: graphHeight + 'px' }"
    >
      <div v-if="loading" class="loading-overlay">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">載入中...</span>
        </div>
        <p class="mt-2">正在建構網絡圖...</p>
      </div>
    </div>

    <!-- 圖例 -->
    <div class="graph-legend mt-3">
      <div class="row">
        <div class="col-md-6">
          <h6><i class="bi bi-circle-fill text-primary"></i> 節點類型</h6>
          <div class="legend-items">
            <span class="legend-item">
              <i class="bi bi-person-circle text-success"></i> 使用者
            </span>
            <span class="legend-item">
              <i class="bi bi-geo-alt-fill text-warning"></i> IP位址
            </span>
            <span class="legend-item">
              <i class="bi bi-file-text text-info"></i> 文章
            </span>
            <span class="legend-item">
              <i class="bi bi-chat-dots text-secondary"></i> 推文
            </span>
          </div>
        </div>
        <div class="col-md-6">
          <h6><i class="bi bi-arrow-right"></i> 關係類型</h6>
          <div class="legend-items">
            <span class="legend-item">
              <span class="line-solid" style="background-color: #28a745;"></span> 發文關係
            </span>
            <span class="legend-item">
              <span class="line-dashed" style="background-color: #17a2b8;"></span> 我推他的文
            </span>
            <span class="legend-item">
              <span class="line-dashed" style="background-color: #fd7e14;"></span> 他推我的文
            </span>
            <span class="legend-item">
              <span class="line-solid" style="background-color: #e83e8c; height: 4px;"></span> 多重關係
            </span>
            <span class="legend-item">
              <span class="line-dotted" style="background-color: #ffc107;"></span> IP使用關係
            </span>
            <span class="legend-item">
              <span class="line-solid" style="background-color: #6f42c1;"></span> 同板發文
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 節點詳情面板 -->
    <div v-if="selectedNode" class="node-details mt-3">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h6 class="mb-0">
            <i :class="getNodeIcon(selectedNode.type)"></i>
            {{ selectedNode.label }}
          </h6>
          <button 
            type="button" 
            class="btn-close" 
            @click="selectedNode = null"
          ></button>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <strong>類型:</strong> {{ getNodeTypeText(selectedNode.type) }}<br>
              <strong>ID:</strong> {{ selectedNode.id }}<br>
              <strong>連接數:</strong> {{ selectedNode.connections || 0 }}
            </div>
            <div class="col-md-6" v-if="selectedNode.metadata">
              <div v-for="(value, key) in selectedNode.metadata" :key="key">
                <strong>{{ key }}:</strong> {{ value }}<br>
              </div>
            </div>
          </div>
          
          <!-- 相關操作 -->
          <div class="mt-3">
            <button 
              class="btn btn-primary btn-sm me-2"
              @click="expandNode(selectedNode)"
              :disabled="loading"
            >
              <i class="bi bi-plus-circle"></i> 展開關聯
            </button>
            <button 
              class="btn btn-outline-secondary btn-sm"
              @click="highlightConnections(selectedNode)"
            >
              <i class="bi bi-eye"></i> 高亮連接
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 統計資訊 -->
    <div class="graph-stats mt-3">
      <div class="row text-center">
        <div class="col-3">
          <div class="stat-item">
            <div class="stat-number">{{ graphStats.nodes }}</div>
            <div class="stat-label">節點</div>
          </div>
        </div>
        <div class="col-3">
          <div class="stat-item">
            <div class="stat-number">{{ graphStats.edges }}</div>
            <div class="stat-label">關係</div>
          </div>
        </div>
        <div class="col-3">
          <div class="stat-item">
            <div class="stat-number">{{ graphStats.users }}</div>
            <div class="stat-label">使用者</div>
          </div>
        </div>
        <div class="col-3">
          <div class="stat-item">
            <div class="stat-number">{{ graphStats.ips }}</div>
            <div class="stat-label">IP位址</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'

// Props
const props = defineProps({
  graphData: {
    type: Object,
    default: () => ({ nodes: [], edges: [] })
  },
  height: {
    type: Number,
    default: 500
  },
  autoLayout: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['nodeClick', 'edgeClick', 'expandNode'])

// 響應式資料
const graphContainer = ref(null)
const loading = ref(false)
const selectedNode = ref(null)
const nodeSize = ref(20)
const graphHeight = ref(props.height)
const currentLayout = ref('force')

// 圖形統計
const graphStats = ref({
  nodes: 0,
  edges: 0,
  users: 0,
  ips: 0
})

// Cytoscape 實例
let cy = null

// 生命週期
onMounted(async () => {
  await initCytoscape()
  if (props.graphData.nodes.length > 0) {
    renderGraph()
  }
})

onUnmounted(() => {
  if (cy) {
    cy.destroy()
  }
})

// 監聽資料變化
watch(() => props.graphData, (newData) => {
  if (newData && newData.nodes.length > 0) {
    renderGraph()
  }
}, { deep: true })

watch(nodeSize, (newSize) => {
  if (cy) {
    cy.style()
      .selector('node')
      .style('width', newSize)
      .style('height', newSize)
      .update()
  }
})

// 初始化 Cytoscape
async function initCytoscape() {
  // 動態載入 Cytoscape (假設已經在 index.html 中引入)
  if (typeof cytoscape === 'undefined') {
    console.error('Cytoscape 未載入')
    return
  }

  cy = cytoscape({
    container: graphContainer.value,
    style: getCytoscapeStyle(),
    layout: getLayoutConfig('force'),
    wheelSensitivity: 0.2,
    minZoom: 0.1,
    maxZoom: 3
  })

  // 綁定事件
  cy.on('tap', 'node', (evt) => {
    const node = evt.target
    selectedNode.value = node.data()
    emit('nodeClick', node.data())
  })

  cy.on('tap', 'edge', (evt) => {
    const edge = evt.target
    emit('edgeClick', edge.data())
  })

  cy.on('tap', (evt) => {
    if (evt.target === cy) {
      selectedNode.value = null
    }
  })
}

// 渲染圖形
function renderGraph() {
  if (!cy) return

  loading.value = true

  try {
    // 轉換資料格式
    const elements = convertDataToCytoscape(props.graphData)
    
    // 清除現有元素並添加新元素
    cy.elements().remove()
    cy.add(elements)

    // 應用佈局
    if (props.autoLayout) {
      const layout = cy.layout(getLayoutConfig(currentLayout.value))
      layout.run()
    }

    // 更新統計
    updateGraphStats()

    // 適應視圖
    nextTick(() => {
      cy.fit()
      loading.value = false
    })

  } catch (error) {
    console.error('渲染圖形失敗:', error)
    loading.value = false
  }
}

// 轉換資料格式為 Cytoscape 格式
function convertDataToCytoscape(data) {
  const elements = []

  // 添加節點
  data.nodes.forEach(node => {
    elements.push({
      data: {
        id: node.id,
        label: node.label || node.id,
        type: node.type || 'default',
        metadata: node.metadata || {},
        connections: node.connections || 0
      },
      classes: `node-${node.type || 'default'}`
    })
  })

  // 添加邊
  data.edges.forEach(edge => {
    elements.push({
      data: {
        id: `${edge.source}-${edge.target}`,
        source: edge.source,
        target: edge.target,
        label: edge.label || '',
        type: edge.type || 'default',
        weight: edge.weight || 1
      },
      classes: `edge-${edge.type || 'default'}`
    })
  })

  return elements
}

// 取得 Cytoscape 樣式
function getCytoscapeStyle() {
  return [
    // 節點樣式
    {
      selector: 'node',
      style: {
        'width': nodeSize.value,
        'height': nodeSize.value,
        'label': 'data(label)',
        'text-valign': 'center',
        'text-halign': 'center',
        'font-size': '12px',
        'font-weight': 'bold',
        'color': '#333',
        'text-outline-width': 2,
        'text-outline-color': '#fff',
        'border-width': 2,
        'border-color': '#666'
      }
    },
    // 使用者節點
    {
      selector: '.node-user',
      style: {
        'background-color': '#28a745',
        'border-color': '#1e7e34'
      }
    },
    // IP 節點
    {
      selector: '.node-ip',
      style: {
        'background-color': '#ffc107',
        'border-color': '#e0a800'
      }
    },
    // 文章節點
    {
      selector: '.node-post',
      style: {
        'background-color': '#17a2b8',
        'border-color': '#138496'
      }
    },
    // 推文節點
    {
      selector: '.node-comment',
      style: {
        'background-color': '#6c757d',
        'border-color': '#545b62'
      }
    },
    // 邊樣式
    {
      selector: 'edge',
      style: {
        'width': 2,
        'line-color': '#999',
        'target-arrow-color': '#999',
        'target-arrow-shape': 'triangle',
        'curve-style': 'bezier',
        'label': 'data(label)',
        'font-size': '10px',
        'text-rotation': 'autorotate'
      }
    },
    // 發文關係
    {
      selector: '.edge-posted',
      style: {
        'line-color': '#28a745',
        'target-arrow-color': '#28a745',
        'line-style': 'solid'
      }
    },
    // 我推他的文
    {
      selector: '.edge-commented_on',
      style: {
        'line-color': '#17a2b8',
        'target-arrow-color': '#17a2b8',
        'line-style': 'dashed',
        'width': 3
      }
    },
    // 他推我的文
    {
      selector: '.edge-commented_by',
      style: {
        'line-color': '#fd7e14',
        'target-arrow-color': '#fd7e14',
        'line-style': 'dashed',
        'width': 3
      }
    },
    // 多重關係
    {
      selector: '.edge-multiple',
      style: {
        'line-color': '#e83e8c',
        'target-arrow-color': '#e83e8c',
        'line-style': 'solid',
        'width': 4
      }
    },
    // IP 使用關係
    {
      selector: '.edge-used_ip',
      style: {
        'line-color': '#ffc107',
        'target-arrow-color': '#ffc107',
        'line-style': 'dotted'
      }
    },
    // 同板發文
    {
      selector: '.edge-posted_same_board',
      style: {
        'line-color': '#6f42c1',
        'target-arrow-color': '#6f42c1',
        'line-style': 'solid',
        'width': 2
      }
    },
    // 選中狀態
    {
      selector: ':selected',
      style: {
        'border-width': 4,
        'border-color': '#dc3545'
      }
    }
  ]
}

// 取得佈局配置
function getLayoutConfig(layoutName) {
  const layouts = {
    force: {
      name: 'cose',
      idealEdgeLength: 100,
      nodeOverlap: 20,
      refresh: 20,
      fit: true,
      padding: 30,
      randomize: false,
      componentSpacing: 100,
      nodeRepulsion: 400000,
      edgeElasticity: 100,
      nestingFactor: 5,
      gravity: 80,
      numIter: 1000,
      initialTemp: 200,
      coolingFactor: 0.95,
      minTemp: 1.0
    },
    circle: {
      name: 'circle',
      fit: true,
      padding: 30,
      boundingBox: undefined,
      avoidOverlap: true,
      radius: undefined,
      startAngle: 3 / 2 * Math.PI,
      sweep: undefined,
      clockwise: true,
      sort: undefined,
      animate: false,
      animationDuration: 500,
      animationEasing: undefined,
      transform: function (node, position) { return position; }
    },
    grid: {
      name: 'grid',
      fit: true,
      padding: 30,
      boundingBox: undefined,
      avoidOverlap: true,
      avoidOverlapPadding: 10,
      nodeDimensionsIncludeLabels: false,
      spacingFactor: undefined,
      condense: false,
      rows: undefined,
      cols: undefined,
      position: function (node) { },
      sort: undefined,
      animate: false,
      animationDuration: 500,
      animationEasing: undefined,
      transform: function (node, position) { return position; }
    }
  }

  return layouts[layoutName] || layouts.force
}

// 更新圖形統計
function updateGraphStats() {
  if (!cy) return

  const nodes = cy.nodes()
  const edges = cy.edges()

  graphStats.value = {
    nodes: nodes.length,
    edges: edges.length,
    users: nodes.filter('[type="user"]').length,
    ips: nodes.filter('[type="ip"]').length
  }
}

// 重置縮放
function resetZoom() {
  if (cy) {
    cy.fit()
  }
}

// 切換佈局
function toggleLayout() {
  const layouts = ['force', 'circle', 'grid']
  const currentIndex = layouts.indexOf(currentLayout.value)
  const nextIndex = (currentIndex + 1) % layouts.length
  currentLayout.value = layouts[nextIndex]

  if (cy) {
    const layout = cy.layout(getLayoutConfig(currentLayout.value))
    layout.run()
  }
}

// 匯出圖片
function exportGraph() {
  if (cy) {
    const png64 = cy.png({ scale: 2, full: true })
    const link = document.createElement('a')
    link.download = `network-graph-${Date.now()}.png`
    link.href = png64
    link.click()
  }
}

// 展開節點
function expandNode(node) {
  emit('expandNode', node)
}

// 高亮連接
function highlightConnections(node) {
  if (!cy) return

  // 重置所有樣式
  cy.elements().removeClass('highlighted faded')

  // 找到相關節點和邊
  const connectedEdges = cy.getElementById(node.id).connectedEdges()
  const connectedNodes = connectedEdges.connectedNodes()

  // 高亮相關元素
  cy.getElementById(node.id).addClass('highlighted')
  connectedEdges.addClass('highlighted')
  connectedNodes.addClass('highlighted')

  // 淡化其他元素
  cy.elements().not(connectedEdges).not(connectedNodes).not(`#${node.id}`).addClass('faded')
}

// 取得節點圖示
function getNodeIcon(type) {
  const icons = {
    user: 'bi bi-person-circle text-success',
    ip: 'bi bi-geo-alt-fill text-warning',
    post: 'bi bi-file-text text-info',
    comment: 'bi bi-chat-dots text-secondary'
  }
  return icons[type] || 'bi bi-circle-fill text-primary'
}

// 取得節點類型文字
function getNodeTypeText(type) {
  const types = {
    user: '使用者',
    ip: 'IP位址',
    post: '文章',
    comment: '推文'
  }
  return types[type] || '未知'
}

// 暴露方法給父組件
defineExpose({
  resetZoom,
  toggleLayout,
  exportGraph,
  highlightConnections
})
</script>

<style scoped>
.network-graph-container {
  width: 100%;
}

.graph-container {
  position: relative;
  background-color: #f8f9fa;
  overflow: hidden;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.graph-legend {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.legend-items {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.9em;
}

.line-solid,
.line-dashed,
.line-dotted {
  width: 20px;
  height: 2px;
  background-color: #666;
}

.line-dashed {
  background-image: repeating-linear-gradient(
    to right,
    #666,
    #666 5px,
    transparent 5px,
    transparent 10px
  );
  background-color: transparent;
}

.line-dotted {
  background-image: repeating-linear-gradient(
    to right,
    #666,
    #666 2px,
    transparent 2px,
    transparent 6px
  );
  background-color: transparent;
}

.node-details {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.graph-stats {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.stat-item {
  padding: 10px;
}

.stat-number {
  font-size: 1.5em;
  font-weight: bold;
  color: #495057;
}

.stat-label {
  font-size: 0.9em;
  color: #6c757d;
  margin-top: 5px;
}

/* Cytoscape 樣式覆蓋 */
:deep(.highlighted) {
  opacity: 1 !important;
}

:deep(.faded) {
  opacity: 0.3 !important;
}
</style>
