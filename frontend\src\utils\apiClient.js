/**
 * API 客戶端工具
 * 統一處理API請求和錯誤處理
 */

import axios from 'axios'
import <PERSON>rrorHandler from './errorHandler.js'

// 建立 axios 實例
const apiClient = axios.create({
  baseURL: '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
})

// 請求攔截器
apiClient.interceptors.request.use(
  (config) => {
    // 添加請求時間戳
    config.metadata = { startTime: new Date() }
    
    // 記錄請求
    console.log(`API請求: ${config.method?.toUpperCase()} ${config.url}`, config.data)
    
    return config
  },
  (error) => {
    console.error('請求攔截器錯誤:', error)
    return Promise.reject(error)
  }
)

// 回應攔截器
apiClient.interceptors.response.use(
  (response) => {
    // 計算請求時間
    const endTime = new Date()
    const duration = endTime - response.config.metadata.startTime
    
    // 記錄回應
    console.log(`API回應: ${response.config.method?.toUpperCase()} ${response.config.url} (${duration}ms)`, response.data)
    
    return response
  },
  (error) => {
    // 計算請求時間
    if (error.config?.metadata?.startTime) {
      const endTime = new Date()
      const duration = endTime - error.config.metadata.startTime
      console.error(`API錯誤: ${error.config.method?.toUpperCase()} ${error.config.url} (${duration}ms)`, error)
    }
    
    return Promise.reject(error)
  }
)

/**
 * API 客戶端類別
 */
class APIClient {
  
  /**
   * 執行 GET 請求
   * @param {string} url - 請求URL
   * @param {Object} params - 查詢參數
   * @param {Object} options - 選項
   */
  static async get(url, params = {}, options = {}) {
    try {
      const response = await apiClient.get(url, { params, ...options })
      return this.handleSuccess(response)
    } catch (error) {
      return this.handleError(error, options)
    }
  }
  
  /**
   * 執行 POST 請求
   * @param {string} url - 請求URL
   * @param {Object} data - 請求資料
   * @param {Object} options - 選項
   */
  static async post(url, data = {}, options = {}) {
    try {
      const response = await apiClient.post(url, data, options)
      return this.handleSuccess(response)
    } catch (error) {
      return this.handleError(error, options)
    }
  }
  
  /**
   * 執行 PUT 請求
   * @param {string} url - 請求URL
   * @param {Object} data - 請求資料
   * @param {Object} options - 選項
   */
  static async put(url, data = {}, options = {}) {
    try {
      const response = await apiClient.put(url, data, options)
      return this.handleSuccess(response)
    } catch (error) {
      return this.handleError(error, options)
    }
  }
  
  /**
   * 執行 DELETE 請求
   * @param {string} url - 請求URL
   * @param {Object} options - 選項
   */
  static async delete(url, options = {}) {
    try {
      const response = await apiClient.delete(url, options)
      return this.handleSuccess(response)
    } catch (error) {
      return this.handleError(error, options)
    }
  }
  
  /**
   * 處理成功回應
   * @param {Object} response - Axios 回應物件
   */
  static handleSuccess(response) {
    const data = response.data
    
    // 檢查API回應格式
    if (data.success === false) {
      // API返回錯誤但HTTP狀態碼是200
      throw new Error(data.error?.message || '操作失敗')
    }
    
    return {
      success: true,
      data: data.data || data,
      message: data.message,
      meta: data.meta,
      timestamp: data.timestamp
    }
  }
  
  /**
   * 處理錯誤回應
   * @param {Object} error - 錯誤物件
   * @param {Object} options - 選項
   */
  static handleError(error, options = {}) {
    const errorInfo = ErrorHandler.handleApiError(error, {
      showAlert: options.showAlert !== false,
      showDetails: options.showDetails || false,
      showCode: options.showCode || false
    })
    
    return {
      success: false,
      error: errorInfo,
      data: null
    }
  }
}

/**
 * 具體的API方法
 */
export const API = {
  
  // 系統相關API
  system: {
    /**
     * 取得系統狀態
     */
    getStatus: () => APIClient.get('/system/status'),
    
    /**
     * 健康檢查
     */
    healthCheck: () => APIClient.get('/system/health'),
    
    /**
     * 取得系統配置
     */
    getConfig: () => APIClient.get('/system/config'),
    
    /**
     * 觸發爬文
     * @param {Object} data - 爬文參數
     */
    triggerCrawl: (data) => APIClient.post('/system/crawl', data),
    
    /**
     * 取得系統日誌
     * @param {number} lines - 行數
     */
    getLogs: (lines = 50) => APIClient.get('/system/logs', { lines }),
    
    /**
     * 清理資料庫
     * @param {Object} data - 清理參數
     */
    clearDatabase: (data) => APIClient.post('/system/database/clear', data),
    
    /**
     * 取得資料庫統計
     */
    getDatabaseStats: () => APIClient.get('/system/database/stats')
  },
  
  // 圖形查詢API
  graph: {
    /**
     * 查詢使用者關聯
     * @param {string} username - 使用者名稱
     * @param {number} maxDepth - 最大深度
     */
    getUserLinks: (username, maxDepth = 3) => 
      APIClient.get('/user-links', { username, max_depth: maxDepth }),
    
    /**
     * 查詢IP使用者
     * @param {string} ip - IP位址
     */
    getIpUsers: (ip) => APIClient.get('/ip-users', { ip }),
    
    /**
     * 查詢文章互動
     * @param {string} postId - 文章ID
     */
    getPostInteractions: (postId) => 
      APIClient.get('/post-interactions', { post_id: postId }),
    
    /**
     * 搜尋使用者
     * @param {string} pattern - 搜尋模式
     * @param {number} limit - 結果限制
     */
    searchUsers: (pattern, limit = 20) => 
      APIClient.get('/search-users', { pattern, limit }),
    
    /**
     * 取得熱門看板
     * @param {number} limit - 結果限制
     */
    getPopularBoards: (limit = 10) => 
      APIClient.get('/popular-boards', { limit }),
    
    /**
     * 列出所有使用者
     * @param {number} limit - 結果限制
     * @param {number} offset - 偏移量
     */
    getUsers: (limit = 100, offset = 0) => 
      APIClient.get('/users', { limit, offset }),
    
    /**
     * 列出所有IP
     * @param {number} limit - 結果限制
     * @param {number} offset - 偏移量
     */
    getIps: (limit = 100, offset = 0) => 
      APIClient.get('/ips', { limit, offset }),
    
    /**
     * 分析使用者IP
     * @param {string} username - 使用者名稱
     */
    analyzeUserIp: (username) => 
      APIClient.get('/user-ip-analysis', { username }),
    
    /**
     * 取得文章列表
     * @param {number} limit - 結果限制
     * @param {number} offset - 偏移量
     * @param {string} board - 看板篩選
     */
    getPosts: (limit = 20, offset = 0, board = '') => 
      APIClient.get('/posts', { limit, offset, board }),
    
    /**
     * 取得圖形網絡資料
     * @param {Object} params - 查詢參數
     */
    getGraphNetwork: (params) => APIClient.get('/graph-network', params)
  }
}

export default APIClient
