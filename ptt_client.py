"""
PTT 自動登入系統 - PTT 客戶端模組
"""
import sys
import os
from datetime import datetime
import PyPtt
from config import Config
from logger import ptt_logger
from board_name_mapper import normalize_board_name, validate_board_name

# 確保正確的編碼設定
if hasattr(sys.stdout, 'reconfigure'):
    sys.stdout.reconfigure(encoding='utf-8')
if hasattr(sys.stderr, 'reconfigure'):
    sys.stderr.reconfigure(encoding='utf-8')

class PTTClient:
    """PTT 客戶端類別"""
    
    def __init__(self):
        self.ptt_bot = None
        self.is_connected = False
        self.is_logged_in = False
        self.last_operation_time = None
    
    def connect(self):
        """連接到 PTT"""
        try:
            ptt_logger.log_operation_start("建立 PTT 物件")

            # 建立 PTT 物件 - 使用正確的 PyPtt API
            self.ptt_bot = PyPtt.API()

            self.is_connected = True
            self.last_operation_time = datetime.now()

            ptt_logger.log_operation_end("建立 PTT 物件", True)
            return True

        except Exception as e:
            ptt_logger.log_connection_error(str(e))
            ptt_logger.log_operation_end("建立 PTT 物件", False, f"錯誤: {e}")
            return False
    
    def login(self, username=None, password=None):
        """登入 PTT"""
        username = username or Config.PTT_USERNAME
        password = password or Config.PTT_PASSWORD

        if not username or not password:
            ptt_logger.error("PTT 帳號或密碼未設定")
            return False

        if not self.is_connected:
            if not self.connect():
                return False

        try:
            ptt_logger.log_login_attempt(username)

            # 執行登入 - 使用測試確認的正確 PyPtt API
            self.ptt_bot.login(
                ptt_id=username,
                ptt_pw=password,
                kick_other_session=True  # 踢掉其他連線
            )

            self.is_logged_in = True
            self.last_operation_time = datetime.now()

            ptt_logger.log_login_success(username)
            return True

        except PyPtt.exceptions.LoginError as e:
            ptt_logger.log_login_failure(username, f"登入錯誤: {e}")
            return False
        except PyPtt.exceptions.UnregisteredUser as e:
            ptt_logger.log_login_failure(username, "未註冊使用者")
            return False
        except PyPtt.exceptions.ConnectionClosed as e:
            ptt_logger.log_login_failure(username, f"連線中斷: {e}")
            return False
        except Exception as e:
            ptt_logger.log_login_failure(username, f"未知錯誤: {e}")
            return False
    
    def logout(self):
        """登出 PTT"""
        try:
            if self.ptt_bot and self.is_logged_in:
                ptt_logger.log_operation_start("登出 PTT")
                self.ptt_bot.logout()
                self.is_logged_in = False
                ptt_logger.log_operation_end("登出 PTT", True)
                
        except Exception as e:
            ptt_logger.error(f"登出時發生錯誤: {e}")
    
    def disconnect(self):
        """斷開連接"""
        try:
            if self.ptt_bot:
                ptt_logger.log_operation_start("斷開 PTT 連接")
                self.logout()  # 先登出
                # PyPtt 1.2.18 使用 logout 來斷開連接
                self.is_connected = False
                self.ptt_bot = None
                ptt_logger.log_operation_end("斷開 PTT 連接", True)

        except Exception as e:
            ptt_logger.error(f"斷開連接時發生錯誤: {e}")
    
    def test_login(self, username=None, password=None):
        """測試登入功能"""
        test_result = {
            'success': False,
            'message': '',
            'details': [],
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        # 取得實際的使用者名稱和密碼
        actual_username = username or Config.PTT_USERNAME
        actual_password = password or Config.PTT_PASSWORD

        try:
            # 步驟1: 連接測試
            test_result['details'].append("正在連接 PTT...")
            if not self.connect():
                test_result['message'] = "連接 PTT 失敗"
                return test_result

            test_result['details'].append("✓ PTT 連接成功")

            # 步驟2: 登入測試
            test_result['details'].append("正在嘗試登入...")
            if not self.login(actual_username, actual_password):
                test_result['message'] = "PTT 登入失敗"
                return test_result

            test_result['details'].append("✓ PTT 登入成功")
            
            # 步驟3: 取得使用者資訊
            try:
                # 使用實際的使用者名稱來取得使用者資訊
                if actual_username:
                    user_info = self.ptt_bot.get_user(actual_username)

                    # 檢查返回的資料類型並正確處理
                    if isinstance(user_info, dict):
                        # 如果是字典，直接使用鍵值
                        ptt_id = user_info.get('ptt_id', user_info.get('id', actual_username))
                        level = user_info.get('level', '未知')
                        career = user_info.get('career', '未知')
                        test_result['details'].append(f"✓ 使用者ID: {ptt_id}")
                        test_result['details'].append(f"✓ 使用者等級: {level}")
                        test_result['details'].append(f"✓ 經驗值: {career}")
                    else:
                        # 如果是物件，使用屬性
                        test_result['details'].append(f"✓ 使用者ID: {user_info.ptt_id}")
                        test_result['details'].append(f"✓ 使用者等級: {user_info.level}")
                else:
                    test_result['details'].append(f"⚠ 使用者名稱為空，無法取得使用者資訊")
                    test_result['details'].append(f"✓ 登入成功")

            except Exception as e:
                test_result['details'].append(f"⚠ 無法取得使用者資訊: {e}")
                # 至少顯示登入的使用者名稱
                test_result['details'].append(f"✓ 登入使用者: {actual_username}")
            
            # 測試成功
            test_result['success'] = True
            test_result['message'] = "PTT 登入測試成功"
            
        except Exception as e:
            test_result['message'] = f"測試過程發生錯誤: {e}"
            test_result['details'].append(f"✗ 錯誤: {e}")
            
        finally:
            # 清理連接
            self.disconnect()
            
        return test_result
    
    def get_status(self):
        """取得連接狀態"""
        return {
            'connected': self.is_connected,
            'logged_in': self.is_logged_in,
            'last_operation': self.last_operation_time.strftime('%Y-%m-%d %H:%M:%S') if self.last_operation_time else None
        }

    def get_board_posts(self, board_name, max_posts=50, target_date=None):
        """取得指定看板的文章列表

        Args:
            board_name (str): 看板名稱
            max_posts (int): 最大文章數量，預設50篇
            target_date (str, optional): 目標日期，格式 'YYYY-MM-DD'，如果指定則只取該日期的文章

        Returns:
            list: 文章資訊列表，每個元素包含 {
                'aid': 文章ID,
                'title': 標題,
                'author': 作者,
                'date': 日期,
                'ip': IP位址 (如果有),
                'board': 看板名稱
            }
        """
        if not self.is_logged_in:
            ptt_logger.error("尚未登入 PTT，無法取得文章列表")
            return []

        # 自動修正看板名稱大小寫
        original_board_name = board_name
        board_validation = validate_board_name(board_name)
        corrected_board_name = board_validation['normalized']

        if board_validation['is_corrected']:
            ptt_logger.info(f"看板名稱自動修正: '{original_board_name}' -> '{corrected_board_name}'")

        try:
            ptt_logger.log_operation_start(f"取得 {corrected_board_name} 看板文章列表")

            # 取得看板最新文章索引
            newest_index = self.ptt_bot.get_newest_index(
                index_type=PyPtt.NewIndex.BOARD,
                board=corrected_board_name
            )
            ptt_logger.info(f"取得 {corrected_board_name} 看板最新索引: {newest_index}")

            # 計算要取得的文章範圍
            start_index = max(1, newest_index - max_posts + 1)

            # 整理文章資訊
            posts_info = []

            # 逐一取得文章
            for i in range(start_index, newest_index + 1):
                try:
                    post = self.ptt_bot.get_post(
                        board=corrected_board_name,
                        index=i
                    )
                    if post:
                        # 處理文章資料 - 支援字典和物件兩種格式
                        def safe_get_value(obj, key, default=''):
                            """安全取得值，支援字典和物件"""
                            if isinstance(obj, dict):
                                value = obj.get(key, default)
                            else:
                                value = getattr(obj, key, default)

                            # 處理編碼問題
                            if isinstance(value, bytes):
                                value = value.decode('utf-8', errors='replace')

                            return value

                        # 檢查文章狀態，跳過已刪除的文章
                        post_status = safe_get_value(post, 'post_status', '')
                        if post_status in ['DELETED_BY_AUTHOR', 'DELETED_BY_MODERATOR']:
                            ptt_logger.debug(f"跳過已刪除文章 {i}: {post_status}")
                            continue

                        # 確保有基本內容才加入
                        title = safe_get_value(post, 'title')
                        author = safe_get_value(post, 'author')

                        if not title and not author:
                            ptt_logger.debug(f"跳過空文章 {i}")
                            continue

                        post_date = safe_get_value(post, 'date')

                        # 如果指定了目標日期，檢查文章日期是否符合
                        if target_date:
                            if not self._is_date_match(post_date, target_date):
                                ptt_logger.debug(f"跳過非目標日期文章 {i}: {post_date}")
                                continue

                        post_info = {
                            'aid': safe_get_value(post, 'aid') or safe_get_value(post, 'id'),
                            'title': title,
                            'author': author,
                            'date': post_date,
                            'ip': safe_get_value(post, 'ip', None),
                            'board': corrected_board_name,
                            'original_board_input': original_board_name  # 記錄原始輸入
                        }
                        posts_info.append(post_info)
                        ptt_logger.debug(f"成功取得文章 {i}: {post_info['title']}")
                except Exception as e:
                    ptt_logger.warning(f"無法取得文章 {i}: {e}")
                    continue

            ptt_logger.log_operation_end(f"取得 {corrected_board_name} 看板文章列表", True, f"成功取得 {len(posts_info)} 篇文章")
            return posts_info

        except Exception as e:
            ptt_logger.log_operation_end(f"取得 {corrected_board_name} 看板文章列表", False, f"錯誤: {e}")
            return []

    def get_post_content(self, post_aid, board_name):
        """取得文章詳細內容和推文

        Args:
            post_aid (str): 文章 AID
            board_name (str): 看板名稱

        Returns:
            dict: 文章詳細資訊 {
                'aid': 文章ID,
                'title': 標題,
                'author': 作者,
                'date': 日期,
                'content': 文章內容,
                'ip': 發文者IP,
                'comments': 推文列表 [
                    {
                        'type': 推文類型 ('推', '噓', '→'),
                        'author': 推文者,
                        'content': 推文內容,
                        'ip': 推文者IP,
                        'time': 推文時間
                    }
                ]
            }
        """
        if not self.is_logged_in:
            ptt_logger.error("尚未登入 PTT，無法取得文章內容")
            return None

        try:
            ptt_logger.log_operation_start(f"取得文章內容 {post_aid}")

            # 嘗試取得文章內容（不需要 goto_board）
            # 注意：PyPtt 的 API 可能因版本而異，這裡使用常見的方法
            post_content = None

            # 方法1：嘗試使用 get_post
            if hasattr(self.ptt_bot, 'get_post'):
                try:
                    post_content = self.ptt_bot.get_post(board=board_name, aid=post_aid)
                except Exception as e:
                    ptt_logger.warning(f"get_post 方法失敗: {e}")

            # 方法2：如果方法1失敗，嘗試其他方法
            if post_content is None and hasattr(self.ptt_bot, 'get_article'):
                try:
                    post_content = self.ptt_bot.get_article(board_name, post_aid)
                except Exception as e:
                    ptt_logger.warning(f"get_article 方法失敗: {e}")

            if post_content is None:
                ptt_logger.error(f"無法取得文章內容: {post_aid}")
                return None

            # 整理文章資訊
            article_info = {
                'aid': post_aid,
                'title': getattr(post_content, 'title', ''),
                'author': getattr(post_content, 'author', ''),
                'date': getattr(post_content, 'date', ''),
                'content': getattr(post_content, 'content', ''),
                'ip': self._extract_ip_from_content(getattr(post_content, 'content', '')),
                'comments': []
            }

            # 處理推文
            comments_data = None

            # 檢查不同的推文資料來源
            if isinstance(post_content, dict):
                comments_data = post_content.get('comments')
            elif hasattr(post_content, 'comments'):
                comments_data = post_content.comments

            if comments_data:
                ptt_logger.info(f"找到 {len(comments_data)} 則推文")
                for comment in comments_data:
                    try:
                        # 處理字典格式的推文
                        if isinstance(comment, dict):
                            comment_type = comment.get('type', '')
                            # 轉換 PyPtt 的推文類型
                            if comment_type == 'ARROW':
                                comment_type = '→'
                            elif comment_type == 'PUSH':
                                comment_type = '推'
                            elif comment_type == 'BOO':
                                comment_type = '噓'

                            comment_info = {
                                'type': comment_type,
                                'author': comment.get('author', ''),
                                'content': comment.get('content', ''),
                                'ip': comment.get('ip', ''),
                                'time': comment.get('time', '')
                            }
                        else:
                            # 處理物件格式的推文
                            comment_type = getattr(comment, 'type', getattr(comment, 'tag', ''))
                            # 轉換 PyPtt 的推文類型
                            if comment_type == 'ARROW':
                                comment_type = '→'
                            elif comment_type == 'PUSH':
                                comment_type = '推'
                            elif comment_type == 'BOO':
                                comment_type = '噓'

                            comment_info = {
                                'type': comment_type,
                                'author': getattr(comment, 'author', ''),
                                'content': getattr(comment, 'content', ''),
                                'ip': getattr(comment, 'ip', ''),
                                'time': getattr(comment, 'time', getattr(comment, 'date', ''))
                            }

                        article_info['comments'].append(comment_info)
                        ptt_logger.debug(f"處理推文: {comment_info}")

                    except Exception as e:
                        ptt_logger.warning(f"處理推文時發生錯誤: {e}")
                        continue

            ptt_logger.log_operation_end(f"取得文章內容 {post_aid}", True,
                                       f"成功取得文章，推文數: {len(article_info['comments'])}")
            return article_info

        except Exception as e:
            ptt_logger.log_operation_end(f"取得文章內容 {post_aid}", False, f"錯誤: {e}")
            return None

    def _extract_ip_from_content(self, content):
        """從文章內容中提取 IP 位址"""
        import re
        if not content:
            return None

        # 尋找 PTT 文章底部的 IP 資訊
        # 格式通常是 "◆ From: xxx.xxx.xxx.xxx"
        ip_pattern = r'◆ From: (\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'
        match = re.search(ip_pattern, content)
        if match:
            return match.group(1)

        # 其他可能的 IP 格式
        ip_pattern2 = r'(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'
        matches = re.findall(ip_pattern2, content)
        if matches:
            # 返回最後一個找到的 IP（通常是發文者的 IP）
            return matches[-1]

        return None

    def _is_date_match(self, post_date, target_date):
        """檢查文章日期是否符合目標日期

        Args:
            post_date (str): 文章日期字串
            target_date (str): 目標日期，格式 'YYYY-MM-DD'

        Returns:
            bool: 是否符合
        """
        if not post_date or not target_date:
            return True

        try:
            # PTT 日期格式可能有多種，嘗試解析
            import re
            from datetime import datetime

            # 移除可能的時間部分，只比較日期
            target_date_obj = datetime.strptime(target_date, '%Y-%m-%d').date()

            # 嘗試解析 PTT 日期格式
            # 常見格式: "12/19", "2025-06-19", "Jun 19", etc.
            post_date_str = str(post_date).strip()

            # 格式1: MM/DD (假設是今年)
            if re.match(r'^\d{1,2}/\d{1,2}$', post_date_str):
                current_year = datetime.now().year
                month, day = post_date_str.split('/')
                post_date_obj = datetime(current_year, int(month), int(day)).date()

            # 格式2: YYYY-MM-DD
            elif re.match(r'^\d{4}-\d{1,2}-\d{1,2}', post_date_str):
                post_date_obj = datetime.strptime(post_date_str[:10], '%Y-%m-%d').date()

            # 格式3: 其他格式，嘗試通用解析
            else:
                # 如果無法解析，返回 True（不過濾）
                ptt_logger.debug(f"無法解析日期格式: {post_date_str}")
                return True

            return post_date_obj == target_date_obj

        except Exception as e:
            ptt_logger.debug(f"日期比較錯誤: {e}")
            return True  # 如果解析失敗，不過濾

    def crawl_board_data(self, board_name, max_posts=10, include_content=True, target_date=None):
        """爬取看板完整資料

        Args:
            board_name (str): 看板名稱
            max_posts (int): 最大文章數量
            include_content (bool): 是否包含文章內容和推文
            target_date (str, optional): 目標日期，格式 'YYYY-MM-DD'

        Returns:
            dict: 看板資料 {
                'board': 看板名稱,
                'crawl_time': 爬取時間,
                'target_date': 目標日期 (如果有指定),
                'posts': [
                    {
                        'aid': 文章ID,
                        'title': 標題,
                        'author': 作者,
                        'date': 日期,
                        'ip': IP位址,
                        'content': 文章內容 (如果 include_content=True),
                        'comments': 推文列表 (如果 include_content=True)
                    }
                ]
            }
        """
        if not self.is_logged_in:
            ptt_logger.error("尚未登入 PTT，無法爬取看板資料")
            return None

        try:
            ptt_logger.log_operation_start(f"爬取 {board_name} 看板資料")

            # 取得文章列表（包含日期過濾）
            posts = self.get_board_posts(board_name, max_posts, target_date)
            if not posts:
                ptt_logger.warning(f"無法取得 {board_name} 看板的文章列表")
                return None

            # 如果需要詳細內容，逐一取得文章內容
            if include_content:
                detailed_posts = []
                for i, post in enumerate(posts):
                    ptt_logger.info(f"正在處理第 {i+1}/{len(posts)} 篇文章: {post['title']}")

                    # 先設定基本的內容和推文欄位
                    post['content'] = ''
                    post['comments'] = []

                    # 嘗試取得詳細內容
                    if post['aid']:
                        detailed_content = self.get_post_content(post['aid'], board_name)
                        if detailed_content:
                            # 只更新內容和推文，保留基本資訊
                            if detailed_content.get('content'):
                                post['content'] = detailed_content['content']
                            if detailed_content.get('comments'):
                                post['comments'] = detailed_content['comments']
                            # 如果詳細內容有更好的 IP 資訊，使用它
                            if detailed_content.get('ip') and not post.get('ip'):
                                post['ip'] = detailed_content['ip']

                    detailed_posts.append(post)

                    # 避免過於頻繁的請求，稍作延遲
                    import time
                    time.sleep(0.5)

                posts = detailed_posts

            board_data = {
                'board': board_name,
                'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'target_date': target_date,
                'posts': posts
            }

            ptt_logger.log_operation_end(f"爬取 {board_name} 看板資料", True,
                                       f"成功爬取 {len(posts)} 篇文章")
            return board_data

        except Exception as e:
            ptt_logger.log_operation_end(f"爬取 {board_name} 看板資料", False, f"錯誤: {e}")
            return None
