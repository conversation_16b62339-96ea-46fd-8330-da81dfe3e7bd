# 開發指南

## 🏗️ 專案架構

本專案採用前後端分離架構：

- **後端**: Flask + Python (API 服務)
- **前端**: Vue 3 + Vite (單頁應用程式)
- **資料庫**: Azure Cosmos DB (Gremlin API)

## 🚀 開發環境設置

### 1. 後端開發

```bash
# 在專案根目錄
py app.py
```

後端服務會在 `http://localhost:5000` 啟動，提供：
- API 端點 (`/api/*`)
- 靜態檔案服務 (編譯後的前端)
- 健康檢查 (`/health`)

### 2. 前端開發

```bash
# 進入前端目錄
cd frontend

# 安裝依賴 (首次)
npm install

# 啟動開發服務
npm run dev
```

前端開發服務會在 `http://localhost:5173` 啟動，具備：
- 熱重載 (Hot Reload)
- API 代理到後端
- Vue DevTools 支援

## 🔄 開發工作流程

### 方案 A: 分離開發模式 (推薦)

1. **啟動後端服務**:
   ```bash
   py app.py
   ```

2. **啟動前端開發服務**:
   ```bash
   cd frontend
   npm run dev
   ```

3. **開發前端**:
   - 在 `http://localhost:5173` 進行前端開發
   - API 請求會自動代理到後端 `http://localhost:5000`
   - 修改前端代碼會即時重載

4. **開發後端**:
   - 修改 Python 代碼後重啟 Flask 服務
   - API 變更會立即反映到前端

### 方案 B: 整合開發模式

1. **編譯前端**:
   ```bash
   cd frontend
   npm run build
   ```

2. **啟動後端**:
   ```bash
   py app.py
   ```

3. **訪問應用**:
   - 在 `http://localhost:5000` 訪問完整應用
   - 前端修改需要重新編譯

## 📁 目錄結構

```
PyLab.Ptt/
├── app.py                 # Flask 主應用程式
├── api/                   # API 路由模組
│   ├── graph_routes.py    # 圖形分析 API
│   └── system_routes.py   # 系統狀態 API
├── frontend/              # Vue 前端專案
│   ├── src/
│   │   ├── App.vue        # 主組件
│   │   └── main.js        # 入口檔案
│   ├── package.json       # 前端依賴
│   └── vite.config.js     # Vite 配置
├── static/                # 靜態檔案
│   └── dist/              # 前端編譯輸出
├── templates/             # Flask 模板 (備用)
└── requirements.txt       # Python 依賴
```

## 🔧 API 代理配置

前端開發服務已配置 API 代理：

```javascript
// vite.config.js
server: {
  proxy: {
    '/api': 'http://localhost:5000',
    '/test-login': 'http://localhost:5000',
    '/status': 'http://localhost:5000',
    '/health': 'http://localhost:5000'
  }
}
```

## 🛠️ 常用開發命令

### 前端
```bash
cd frontend

# 開發模式
npm run dev

# 編譯生產版本
npm run build

# 安裝新依賴
npm install <package-name>
```

### 後端
```bash
# 啟動開發服務
py app.py

# 安裝新依賴
pip install <package-name>

# 更新 requirements.txt
pip freeze > requirements.txt

# 執行測試
py run_all_tests.py
```

## 🔍 除錯技巧

### 前端除錯
- 使用瀏覽器開發者工具
- Vue DevTools 擴充功能
- 檢查 Network 標籤查看 API 請求

### 後端除錯
- 查看 Flask 控制台輸出
- 檢查 `logs/` 目錄下的日誌檔案
- 使用 `/health` 端點檢查服務狀態

## 📝 開發注意事項

1. **API 變更**: 修改 API 後需要更新前端對應的請求
2. **環境變數**: 確保 `.env` 檔案配置正確
3. **CORS**: 開發模式下已透過代理解決跨域問題
4. **編碼**: 確保中文字符正確顯示
5. **測試**: 修改後執行相關測試確保功能正常

## 🚀 部署準備

部署前執行：

```bash
# 編譯前端
cd frontend
npm run build

# 確認編譯檔案
ls -la ../static/dist/

# 測試完整應用
cd ..
py app.py
```

訪問 `http://localhost:5000` 確認應用正常運行。
