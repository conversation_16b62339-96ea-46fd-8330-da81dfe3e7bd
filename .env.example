# PTT 自動登入系統 - 環境變數設定範例
# 複製此檔案為 .env 並填入實際的設定值

# PTT 連接設定
PTT_USERNAME=your_ptt_username
PTT_PASSWORD=your_ptt_password
PTT_HOST=ptt.cc
PTT_PORT=23

# 系統設定
LOG_LEVEL=INFO
TIMEZONE=Asia/Taipei
POST_SCHEDULE=08:00

# Flask 設定
SECRET_KEY=your-secret-key-here
FLASK_DEBUG=False

# 重試設定
MAX_RETRY_ATTEMPTS=3
RETRY_DELAY=5

# 連接超時設定 (秒)
CONNECTION_TIMEOUT=30
LOGIN_TIMEOUT=10

# Azure Cosmos DB 設定
COSMOS_DB_ENDPOINT=wss://your-cosmos-account.gremlin.cosmos.azure.com:443/
COSMOS_DB_KEY=your_cosmos_db_primary_key
COSMOS_DB_DATABASE=ptt_graph_db
COSMOS_DB_COLLECTION=ptt_graph

# 爬蟲設定
DEFAULT_BOARDS=Test
MAX_POSTS_PER_BOARD=50
CRAWL_INTERVAL_HOURS=24
