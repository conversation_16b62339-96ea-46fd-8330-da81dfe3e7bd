{% extends "base.html" %}

{% block title %}IP 分析 - PTT 圖形分析系統{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="bi bi-router"></i> IP 分析
        </h1>
        <p class="lead">分析特定 IP 位址的使用者活動，識別可能的多重帳號或共用網路環境。</p>
    </div>
</div>

<!-- 搜尋表單 -->
<div class="search-form">
    <form id="ipSearchForm">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="ipAddress" class="form-label">IP 位址</label>
                    <input type="text" class="form-control" id="ipAddress" name="ipAddress" 
                           placeholder="例如: ***********" pattern="^(\d{1,3}\.){3}\d{1,3}$" required>
                    <div class="form-text">請輸入有效的 IPv4 位址</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="mb-3">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search"></i> 分析 IP
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="clearResults()">
                            <i class="bi bi-arrow-clockwise"></i> 清除
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- 載入狀態 -->
<div id="loadingSpinner" class="loading-spinner">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">載入中...</span>
    </div>
    <p class="mt-2">正在分析 IP 使用情況...</p>
</div>

<!-- 結果區域 -->
<div id="resultsContainer" style="display: none;">
    <!-- IP 基本資訊 -->
    <div class="card result-card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-info-circle"></i> IP 基本資訊
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number" id="usersCount">0</div>
                        <div class="text-muted">使用者數量</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number" id="postsCount">0</div>
                        <div class="text-muted">發文數量</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h6>IP 位址: <span id="currentIp" class="text-primary"></span></h6>
                    <p class="mb-0">此 IP 的活動時間範圍和使用模式分析</p>
                </div>
            </div>
        </div>
    </div>

    <!-- IP 使用者網絡圖 -->
    <div class="card result-card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-diagram-3"></i> IP 使用者網絡
            </h5>
            <div class="graph-controls">
                <button class="btn btn-sm btn-outline-primary" onclick="fitGraph()">
                    <i class="bi bi-arrows-fullscreen"></i> 適應視窗
                </button>
                <button class="btn btn-sm btn-outline-secondary" onclick="resetGraph()">
                    <i class="bi bi-arrow-clockwise"></i> 重置佈局
                </button>
                <button class="btn btn-sm btn-outline-info" onclick="exportGraph()">
                    <i class="bi bi-download"></i> 匯出圖片
                </button>
            </div>
        </div>
        <div class="card-body">
            <div id="ipGraph" class="graph-container"></div>
            
            <!-- 圖例 -->
            <div class="graph-legend">
                <h6>圖例</h6>
                <div class="legend-item">
                    <span class="legend-color ip-node"></span>
                    <span>IP 位址</span>
                </div>
                <div class="legend-item">
                    <span class="legend-color user-node"></span>
                    <span>使用者</span>
                </div>
                <div class="legend-item">
                    <span class="legend-color post-node"></span>
                    <span>文章</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 詳細資訊 -->
    <div class="row">
        <!-- 使用者列表 -->
        <div class="col-md-6">
            <div class="card result-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-people"></i> 使用此 IP 的帳號
                    </h5>
                </div>
                <div class="card-body">
                    <div id="usersList"></div>
                </div>
            </div>
        </div>

        <!-- 文章列表 -->
        <div class="col-md-6">
            <div class="card result-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-file-text"></i> 來自此 IP 的文章
                    </h5>
                </div>
                <div class="card-body">
                    <div id="postsList"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 時間軸分析 -->
    <div class="card result-card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-clock-history"></i> 活動時間軸
            </h5>
        </div>
        <div class="card-body">
            <div id="timelineContainer">
                <p class="text-muted">時間軸功能開發中...</p>
            </div>
        </div>
    </div>

    <!-- 風險評估 -->
    <div class="card result-card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-shield-exclamation"></i> 風險評估
            </h5>
        </div>
        <div class="card-body">
            <div id="riskAssessment"></div>
        </div>
    </div>
</div>

<!-- 錯誤訊息 -->
<div id="errorContainer"></div>
{% endblock %}

{% block extra_js %}
<script>
let ipGraph = null;

// 表單提交處理
document.getElementById('ipSearchForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const ipAddress = document.getElementById('ipAddress').value.trim();
    
    if (!ipAddress) {
        showError('請輸入 IP 位址', 'errorContainer');
        return;
    }
    
    // 驗證 IP 格式
    const ipPattern = /^(\d{1,3}\.){3}\d{1,3}$/;
    if (!ipPattern.test(ipAddress)) {
        showError('請輸入有效的 IP 位址格式', 'errorContainer');
        return;
    }
    
    // 顯示載入狀態
    showLoading('loadingSpinner');
    document.getElementById('resultsContainer').style.display = 'none';
    document.getElementById('errorContainer').innerHTML = '';
    
    try {
        // 呼叫 API
        const response = await apiRequest(`/api/ip-users?ip=${encodeURIComponent(ipAddress)}`);
        
        if (response.success) {
            displayIpAnalysis(response.data);
        } else {
            showError(response.error || '查詢失敗', 'errorContainer');
        }
    } catch (error) {
        showError(`查詢失敗: ${error.message}`, 'errorContainer');
    } finally {
        hideLoading('loadingSpinner');
    }
});

// 顯示 IP 分析結果
function displayIpAnalysis(data) {
    // 更新基本資訊
    document.getElementById('currentIp').textContent = data.ip;
    document.getElementById('usersCount').textContent = data.users ? data.users.length : 0;
    document.getElementById('postsCount').textContent = data.posts_from_ip ? data.posts_from_ip.length : 0;
    
    // 建立 IP 網絡圖
    createIpGraph(data);
    
    // 顯示使用者列表
    displayUsersList(data.users || []);
    
    // 顯示文章列表
    displayPostsList(data.posts_from_ip || []);
    
    // 顯示風險評估
    displayRiskAssessment(data);
    
    // 顯示結果區域
    document.getElementById('resultsContainer').style.display = 'block';
}

// 建立 IP 網絡圖
function createIpGraph(data) {
    const container = document.getElementById('ipGraph');
    
    // 準備圖形資料
    const elements = [];
    
    // 添加中心 IP 節點
    elements.push({
        data: { 
            id: `ip_${data.ip}`, 
            label: data.ip,
            type: 'ip',
            isCenter: true
        }
    });
    
    // 添加使用者節點
    if (data.users) {
        data.users.forEach(user => {
            const userId = user.username || user;
            elements.push({
                data: { 
                    id: userId, 
                    label: userId,
                    type: 'user'
                }
            });
            
            // 添加 IP 到使用者的邊
            elements.push({
                data: { 
                    source: `ip_${data.ip}`, 
                    target: userId,
                    type: 'used_by'
                }
            });
        });
    }
    
    // 添加文章節點
    if (data.posts_from_ip) {
        data.posts_from_ip.slice(0, 10).forEach((post, index) => {  // 限制顯示數量
            const postId = `post_${index}`;
            elements.push({
                data: { 
                    id: postId, 
                    label: post.title ? post.title.substring(0, 20) + '...' : `文章 ${index + 1}`,
                    type: 'post',
                    fullTitle: post.title
                }
            });
            
            // 添加 IP 到文章的邊
            elements.push({
                data: { 
                    source: `ip_${data.ip}`, 
                    target: postId,
                    type: 'posted_from'
                }
            });
        });
    }
    
    // 建立 Cytoscape 圖形
    ipGraph = cytoscape({
        container: container,
        elements: elements,
        style: [
            {
                selector: 'node',
                style: {
                    'background-color': '#007bff',
                    'label': 'data(label)',
                    'text-valign': 'center',
                    'text-halign': 'center',
                    'color': 'white',
                    'text-outline-width': 2,
                    'text-outline-color': '#000',
                    'font-size': '10px',
                    'width': 50,
                    'height': 50,
                    'text-wrap': 'wrap',
                    'text-max-width': 80
                }
            },
            {
                selector: 'node[type="ip"]',
                style: {
                    'background-color': '#28a745',
                    'shape': 'rectangle',
                    'width': 80,
                    'height': 40
                }
            },
            {
                selector: 'node[type="user"]',
                style: {
                    'background-color': '#007bff',
                    'shape': 'ellipse'
                }
            },
            {
                selector: 'node[type="post"]',
                style: {
                    'background-color': '#ffc107',
                    'color': '#000',
                    'text-outline-color': '#fff',
                    'shape': 'rectangle',
                    'width': 60,
                    'height': 30
                }
            },
            {
                selector: 'node[isCenter]',
                style: {
                    'background-color': '#dc3545',
                    'width': 100,
                    'height': 50,
                    'font-size': '12px',
                    'font-weight': 'bold'
                }
            },
            {
                selector: 'edge',
                style: {
                    'width': 2,
                    'line-color': '#ccc',
                    'target-arrow-color': '#ccc',
                    'target-arrow-shape': 'triangle',
                    'curve-style': 'bezier'
                }
            },
            {
                selector: 'edge[type="used_by"]',
                style: {
                    'line-color': '#007bff',
                    'target-arrow-color': '#007bff'
                }
            },
            {
                selector: 'edge[type="posted_from"]',
                style: {
                    'line-color': '#ffc107',
                    'target-arrow-color': '#ffc107'
                }
            }
        ],
        layout: {
            name: 'cose-bilkent',
            animate: true,
            animationDuration: 1000,
            nodeDimensionsIncludeLabels: true
        }
    });
    
    // 添加點擊事件
    ipGraph.on('tap', 'node', function(evt) {
        const node = evt.target;
        const nodeData = node.data();
        
        let info = `節點資訊:\n類型: ${nodeData.type}\n標籤: ${nodeData.label}`;
        if (nodeData.fullTitle) {
            info += `\n完整標題: ${nodeData.fullTitle}`;
        }
        
        alert(info);
    });
}

// 顯示使用者列表
function displayUsersList(users) {
    const container = document.getElementById('usersList');
    
    if (users.length === 0) {
        container.innerHTML = '<div class="text-muted">無使用者記錄</div>';
        return;
    }
    
    let html = '<div class="list-group list-group-flush">';
    users.forEach(user => {
        const username = user.username || user;
        const firstSeen = user.first_seen || '未知';
        const lastSeen = user.last_seen || '未知';
        
        html += `
            <div class="list-group-item">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">
                        <i class="bi bi-person"></i> ${username}
                    </h6>
                    <button class="btn btn-sm btn-outline-primary" onclick="analyzeUser('${username}')">
                        分析
                    </button>
                </div>
                <p class="mb-1">首次出現: ${firstSeen}</p>
                <small>最後出現: ${lastSeen}</small>
            </div>
        `;
    });
    html += '</div>';
    
    container.innerHTML = html;
}

// 顯示文章列表
function displayPostsList(posts) {
    const container = document.getElementById('postsList');
    
    if (posts.length === 0) {
        container.innerHTML = '<div class="text-muted">無文章記錄</div>';
        return;
    }
    
    let html = '<div class="list-group list-group-flush">';
    posts.slice(0, 10).forEach(post => {  // 只顯示前10篇
        html += `
            <div class="list-group-item">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">${post.title || '無標題'}</h6>
                    <small>${post.date || ''}</small>
                </div>
                <p class="mb-1">看板: ${post.board || '未知'}</p>
                <small>文章ID: ${post.post_id || '未知'}</small>
            </div>
        `;
    });
    
    if (posts.length > 10) {
        html += `<div class="list-group-item text-muted">還有 ${posts.length - 10} 篇文章...</div>`;
    }
    
    html += '</div>';
    container.innerHTML = html;
}

// 顯示風險評估
function displayRiskAssessment(data) {
    const container = document.getElementById('riskAssessment');
    
    const userCount = data.users ? data.users.length : 0;
    const postCount = data.posts_from_ip ? data.posts_from_ip.length : 0;
    
    let riskLevel = 'low';
    let riskText = '低風險';
    let riskColor = 'success';
    let riskDescription = '此 IP 的使用模式正常。';
    
    if (userCount > 5) {
        riskLevel = 'high';
        riskText = '高風險';
        riskColor = 'danger';
        riskDescription = '此 IP 有多個使用者帳號，可能存在多重帳號或共用網路環境。';
    } else if (userCount > 2) {
        riskLevel = 'medium';
        riskText = '中風險';
        riskColor = 'warning';
        riskDescription = '此 IP 有少數使用者帳號，建議進一步調查。';
    }
    
    const html = `
        <div class="alert alert-${riskColor}" role="alert">
            <h6 class="alert-heading">
                <i class="bi bi-shield-${riskLevel === 'high' ? 'exclamation' : riskLevel === 'medium' ? 'check' : 'fill-check'}"></i>
                風險等級: ${riskText}
            </h6>
            <p>${riskDescription}</p>
            <hr>
            <div class="row">
                <div class="col-md-4">
                    <strong>使用者數量:</strong> ${userCount}
                </div>
                <div class="col-md-4">
                    <strong>文章數量:</strong> ${postCount}
                </div>
                <div class="col-md-4">
                    <strong>活動密度:</strong> ${postCount > 0 ? (postCount / userCount).toFixed(1) : 0} 篇/人
                </div>
            </div>
        </div>
    `;
    
    container.innerHTML = html;
}

// 圖形控制函數
function fitGraph() {
    if (ipGraph) {
        ipGraph.fit();
    }
}

function resetGraph() {
    if (ipGraph) {
        ipGraph.layout({ name: 'cose-bilkent', animate: true }).run();
    }
}

function exportGraph() {
    if (ipGraph) {
        const png64 = ipGraph.png({ scale: 2 });
        const link = document.createElement('a');
        link.href = png64;
        link.download = `ip_analysis_${document.getElementById('ipAddress').value.replace(/\./g, '_')}.png`;
        link.click();
    }
}

// 分析使用者
function analyzeUser(username) {
    // 跳轉到使用者分析頁面
    window.location.href = `/user-analysis?username=${encodeURIComponent(username)}`;
}

// 清除結果
function clearResults() {
    document.getElementById('resultsContainer').style.display = 'none';
    document.getElementById('errorContainer').innerHTML = '';
    document.getElementById('ipAddress').value = '';
    if (ipGraph) {
        ipGraph.destroy();
        ipGraph = null;
    }
}
</script>
{% endblock %}
