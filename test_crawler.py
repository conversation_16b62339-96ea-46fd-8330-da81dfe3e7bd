#!/usr/bin/env python3
"""
[?] PTT [?]
"""

import json
from crawler import PTTCrawler
from config import Config

def test_crawler_basic():
    """[?]"""
    print("=== [?] ===")
    
    # [?]
    if not Config.PTT_USERNAME or not Config.PTT_PASSWORD:
        print("[WARN] [?] PTT [?]")
        return False
    
    try:
        # [?]
        crawler = PTTCrawler()
        print("[OK] [?]")
        
        # [?]Test [?]
        print("1. [?] Test [?]...")
        board_data = crawler.crawl_single_board('Test', max_posts=3)
        
        if board_data:
            print("[OK] [?]")
            print(f"   [?]: {board_data['board']}")
            print(f"   [?]: {board_data['posts_count']}")
            print(f"   [?]: {len(board_data['users'])}")
            print(f"   IP [?]: {len(board_data['ips'])}")
            
            # [?]
            if board_data['posts']:
                first_post = board_data['posts'][0]
                print(f"   [?]: {first_post['title']}")
                print(f"   [?]: {first_post['author']}")
                print(f"   [?]: {len(first_post['comments'])}")
        else:
            print("[FAIL] [?]")
            return False
        
        return True
        
    except Exception as e:
        print(f"[FAIL] [?]: {e}")
        return False

def test_crawler_multiple_boards():
    """[?]"""
    print("\n=== [?] ===")
    
    # [?]
    if not Config.PTT_USERNAME or not Config.PTT_PASSWORD:
        print("[WARN] [?] PTT [?]")
        return False
    
    try:
        # [?]
        crawler = PTTCrawler()
        
        # [?]
        print("1. [?]...")
        board_list = ['Test']  # [?] Test [?]
        
        crawl_result = crawler.crawl_boards(board_list, max_posts_per_board=2)
        
        if crawl_result['success']:
            print("[OK] [?]")
            print(f"   [?]: {crawl_result['crawl_time']}")
            print(f"   [?]: {crawl_result['summary']['boards_count']}")
            print(f"   [?]: {crawl_result['summary']['total_posts']}")
            print(f"   [?]: {crawl_result['summary']['total_users']}")
            print(f"   [?] IP [?]: {crawl_result['summary']['total_ips']}")
            
            # [?]
            filename = crawler.save_crawl_result(crawl_result, "test_crawl_result.json")
            print(f"[OK] [?]: {filename}")
            
        else:
            print("[FAIL] [?]")
            return False
        
        return True
        
    except Exception as e:
        print(f"[FAIL] [?]: {e}")
        return False

def test_graph_data_generation():
    """[?]"""
    print("\n=== [?] ===")
    
    # [?]
    if not Config.PTT_USERNAME or not Config.PTT_PASSWORD:
        print("[WARN] [?] PTT [?]")
        return False
    
    try:
        # [?]
        crawler = PTTCrawler()
        
        # [?]
        print("1. [?]...")
        board_list = ['Test']
        crawl_result = crawler.crawl_boards(board_list, max_posts_per_board=2)
        
        if not crawl_result['success']:
            print("[FAIL] [?]")
            return False
        
        # [?]
        print("2. [?]...")
        graph_data = crawler.generate_graph_data(crawl_result)
        
        print("[OK] [?]")
        print(f"   [?]: {len(graph_data['vertices']['users'])}")
        print(f"   IP [?]: {len(graph_data['vertices']['ips'])}")
        print(f"   [?]: {len(graph_data['vertices']['posts'])}")
        print(f"   [?]: {len(graph_data['edges']['posted'])}")
        print(f"   [?]: {len(graph_data['edges']['commented'])}")
        print(f"   [?]IP[?]: {len(graph_data['edges']['used_ip'])}")
        print(f"   [?]IP[?]: {len(graph_data['edges']['from_ip'])}")
        
        # [?]
        with open("test_graph_data.json", 'w', encoding='utf-8') as f:
            json.dump(graph_data, f, ensure_ascii=False, indent=2)
        print("[OK] [?]: test_graph_data.json")
        
        # [?]
        if graph_data['vertices']['users']:
            print("\n   [?]:")
            for user in graph_data['vertices']['users'][:3]:
                print(f"     - {user['id']}: {user['properties']}")
        
        if graph_data['edges']['posted']:
            print("\n   [?]:")
            for edge in graph_data['edges']['posted'][:3]:
                print(f"     - {edge['from']} -> {edge['to']}: {edge['properties']}")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] [?]: {e}")
        return False

def test_json_serialization():
    """[?] JSON [?]"""
    print("\n=== [?] JSON [?] ===")
    
    try:
        crawler = PTTCrawler()
        
        # [?] set[?]
        test_data = {
            'users': {'user1', 'user2', 'user3'},
            'ips': {'***********', '********'},
            'nested': {
                'more_users': {'user4', 'user5'},
                'list_with_sets': [{'a', 'b'}, {'c', 'd'}]
            }
        }
        
        print("1. [?] JSON [?]...")
        serializable_data = crawler._make_json_serializable(test_data)
        
        # [?]
        json_str = json.dumps(serializable_data, ensure_ascii=False, indent=2)
        print("[OK] JSON [?]")
        
        # [?]
        parsed_data = json.loads(json_str)
        print(f"   [?]: {parsed_data['users']}")
        print(f"   IP [?]: {parsed_data['ips']}")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] [?]: {e}")
        return False

def main():
    """[?]"""
    print("PTT [?]")
    print("=" * 50)
    
    # [?]
    config_errors = Config.validate_config()
    if config_errors:
        print("[?]:")
        for error in config_errors:
            print(f"  - {error}")
        print("\n[?] PTT_USERNAME [?] PTT_PASSWORD [?]")
        print("[?]...")
        
        # [?] JSON [?]
        if test_json_serialization():
            print("\n[OK] JSON [?]")
            return 0
        else:
            print("\n[FAIL] JSON [?]")
            return 1
    
    print(f"[?]: {Config.PTT_USERNAME}")
    print()
    
    # [?]
    tests = [
        ("JSON [?]", test_json_serialization),
        ("[?]", test_crawler_basic),
        ("[?]", test_crawler_multiple_boards),
        ("[?]", test_graph_data_generation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"[OK] {test_name} [?]")
            else:
                print(f"[FAIL] {test_name} [?]")
        except Exception as e:
            print(f"[FAIL] {test_name} [?]: {e}")
    
    print("\n" + "=" * 50)
    print(f"[?]: {passed}/{total} [?]")
    
    if passed == total:
        print("[SUCCESS] [?]")
        return 0
    else:
        print("[?] [?] PTT [?]")
        return 1

if __name__ == "__main__":
    exit(main())
