# PTT 自動登入發文系統 - 系統架構文件

## 專案概述

### 專案用途
本專案旨在建立一個自動化系統，能夠定期自動登入PTT（批踢踢實業坊）並執行發文操作。系統主要功能包括：

- **自動登入PTT**：使用WebSocket連接自動化登入流程
- **自動發文**：根據預設內容或動態內容自動發布文章
- **排程執行**：支援每日定時執行任務
- **日誌記錄**：完整記錄操作過程與結果
- **錯誤處理**：異常情況的自動重試與通知機制

### 應用場景
- 定期公告發布
- 社群互動維護
- 資訊更新推播
- 活動宣傳自動化

## 技術架構

### 系統架構圖（簡化版）
```
┌─────────────────┐    ┌─────────────────┐
│   排程觸發      │───►│   PTT WebSocket │
│   (WebApp內建)  │    │   wss://ws.ptt.cc │
└─────────────────┘    └─────────────────┘
         │
         ▼
┌──────────────────┐
│   Azure WebApp   │
│   (Python Flask  │
│   + 內建Storage) │
└──────────────────┘
```

### 核心技術棧

#### 後端框架
- **Python 3.9+**：主要開發語言
- **Flask**：輕量級Web框架，處理HTTP請求
- **PyPtt**：專門的PTT連接套件，封裝所有PTT操作

#### PTT連接技術
- **PyPtt Library**：使用現成的PTT Python套件
  - 自動處理WebSocket連接
  - 內建編碼轉換功能
  - 封裝登入、發文、瀏覽等所有操作
- **優勢**：無需自行處理底層WebSocket協議和PTT介面邏輯

#### 部署與基礎設施
- **Azure Web App**：主要運行環境
- **Azure Timer Functions**：定時觸發任務
- **Azure Storage Account**：儲存日誌和配置文件
- **Azure Key Vault**：安全儲存PTT帳號密碼

## Azure WebApp 部署架構（簡化版）

### 部署配置

#### Web App 設定
- **運行時環境**：Python 3.9 Linux
- **定價層**：Free F1 或 Shared D1（大幅降低成本）
- **區域**：East Asia

#### 環境變數配置（直接在WebApp設定）
```
PTT_USERNAME=你的PTT帳號
PTT_PASSWORD=你的PTT密碼
LOG_LEVEL=INFO
TIMEZONE=Asia/Taipei
POST_SCHEDULE=08:00  # 每日發文時間
```

#### 簡化功能
- **內建排程**：使用APScheduler在應用程式內部處理定時任務
- **檔案日誌**：直接寫入WebApp的檔案系統
- **基本監控**：使用Python logging模組

### 執行流程（簡化版）

#### 1. 應用程式啟動
```
Flask App 啟動
    ↓
初始化APScheduler
    ↓
設定每日定時任務（如08:00執行）
    ↓
應用程式持續運行
```

#### 2. PTT操作流程（使用PyPtt）
```
定時觸發
    ↓
初始化PyPtt物件
    ↓
ptt.login(username, password)
    ↓
ptt.goto_board(board_name)
    ↓
ptt.post(title, content)
    ↓
ptt.logout()
    ↓
記錄執行結果到本地檔案
```

#### 3. 錯誤處理機制
- **連接超時**：自動重試3次
- **登入失敗**：記錄錯誤並發送通知
- **發文失敗**：保存草稿並安排重試
- **網路異常**：指數退避重試策略

### 安全性考量（簡化版）

#### 基本安全
- 帳號密碼直接設定在WebApp環境變數中
- 使用HTTPS傳輸
- 基本的錯誤處理和日誌記錄

#### 注意事項
- 此簡化版本適合個人使用或測試環境
- 生產環境建議使用Key Vault儲存敏感資訊

## 監控與維運

### 日誌記錄
- **操作日誌**：記錄每次執行的詳細步驟
- **錯誤日誌**：異常情況的完整堆疊資訊
- **效能日誌**：連接時間、操作耗時等指標

### 監控指標
- **成功率**：登入和發文的成功百分比
- **回應時間**：從觸發到完成的總耗時
- **錯誤率**：異常情況的發生頻率

### 警報設定
- 連續3次執行失敗時發送郵件通知
- 響應時間超過5分鐘時發送警報
- Azure資源使用率超過80%時通知

## 部署流程

### 1. 環境準備
- 建立Azure Resource Group
- 配置Azure Web App和相關服務
- 設定Key Vault和儲存帳戶

### 2. 程式部署
- 使用Azure DevOps或GitHub Actions
- 設定CI/CD pipeline
- 配置環境變數和連接字串

### 3. 測試驗證
- 執行端到端測試
- 驗證定時任務觸發
- 確認日誌記錄和監控功能

### 4. 上線運行
- 啟用Production環境
- 設定監控警報
- 建立運維文檔

## 成本估算（簡化版）

### Azure服務費用（月）
- **Web App Free F1**：$0 USD（有使用限制）
- **Web App Shared D1**：約 $3 USD（如需更多資源）
- **總計**：$0-3 USD/月

### Free F1 限制
- 每日60分鐘CPU時間
- 1GB儲存空間
- 無Always On功能
- 適合低頻率使用（每日一次發文完全夠用）

### 成本優化
- 使用Free F1方案，成本為零
- 如需要Always On功能，可升級到Shared D1
- 無需額外的Storage、Functions、Key Vault等服務