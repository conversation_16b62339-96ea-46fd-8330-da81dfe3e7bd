"""
測試錯誤處理系統
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from error_handler import ErrorHandler, ErrorCode, APIError
from config import Config
import json


def test_error_handler():
    """測試錯誤處理器"""
    print("=== 測試錯誤處理器 ===")
    
    # 測試建立錯誤回應
    response = ErrorHandler.create_error_response(
        ErrorCode.MISSING_PARAMETER,
        "缺少使用者名稱參數",
        {"parameter": "username"}
    )
    
    print("錯誤回應測試:")
    print(response.get_data(as_text=True))
    print(f"狀態碼: {response.status_code}")
    print()
    
    # 測試成功回應
    success_response = ErrorHandler.create_success_response(
        {"users": ["user1", "user2"]},
        "查詢成功",
        {"count": 2}
    )
    
    print("成功回應測試:")
    print(success_response.get_data(as_text=True))
    print()


def test_config_validation():
    """測試配置驗證"""
    print("=== 測試配置驗證 ===")
    
    # 測試 PTT 配置驗證
    ptt_result = Config.validate_config()
    print("PTT 配置驗證結果:")
    print(f"是否有效: {ptt_result['is_valid']}")
    print(f"錯誤數量: {len(ptt_result['errors'])}")
    print(f"警告數量: {len(ptt_result['warnings'])}")
    
    if ptt_result['errors']:
        print("錯誤詳情:")
        for error in ptt_result['errors']:
            print(f"  - {error['field']}: {error['message']}")
            print(f"    建議: {error['suggestion']}")
    
    if ptt_result['warnings']:
        print("警告詳情:")
        for warning in ptt_result['warnings']:
            print(f"  - {warning['field']}: {warning['message']}")
            print(f"    建議: {warning['suggestion']}")
    
    print()
    
    # 測試 Cosmos 配置驗證
    cosmos_result = Config.validate_cosmos_config()
    print("Cosmos DB 配置驗證結果:")
    print(f"是否有效: {cosmos_result['is_valid']}")
    print(f"錯誤數量: {len(cosmos_result['errors'])}")
    print(f"警告數量: {len(cosmos_result['warnings'])}")
    
    if cosmos_result['errors']:
        print("錯誤詳情:")
        for error in cosmos_result['errors']:
            print(f"  - {error['field']}: {error['message']}")
            print(f"    建議: {error['suggestion']}")
    
    if cosmos_result['warnings']:
        print("警告詳情:")
        for warning in cosmos_result['warnings']:
            print(f"  - {warning['field']}: {warning['message']}")
            print(f"    建議: {warning['suggestion']}")
    
    print()


def test_api_error():
    """測試 API 錯誤類別"""
    print("=== 測試 API 錯誤類別 ===")
    
    try:
        raise APIError(
            ErrorCode.USER_NOT_FOUND,
            "找不到使用者 'test_user'",
            {"username": "test_user"},
            404
        )
    except APIError as e:
        print(f"錯誤代碼: {e.error_code.value}")
        print(f"錯誤類型: {e.error_code.name}")
        print(f"錯誤訊息: {e.message}")
        print(f"錯誤詳情: {e.details}")
        print(f"HTTP 狀態碼: {e.status_code}")
        
        # 測試錯誤處理
        response = ErrorHandler.handle_exception(e)
        print("處理後的回應:")
        print(response.get_data(as_text=True))
    
    print()


def test_exception_handling():
    """測試異常處理"""
    print("=== 測試異常處理 ===")
    
    # 測試 ValueError
    try:
        raise ValueError("無效的數值格式")
    except Exception as e:
        response = ErrorHandler.handle_exception(e)
        print("ValueError 處理:")
        print(response.get_data(as_text=True))
    
    print()
    
    # 測試 KeyError
    try:
        raise KeyError("missing_key")
    except Exception as e:
        response = ErrorHandler.handle_exception(e)
        print("KeyError 處理:")
        print(response.get_data(as_text=True))
    
    print()
    
    # 測試 ConnectionError
    try:
        raise ConnectionError("無法連接到資料庫")
    except Exception as e:
        response = ErrorHandler.handle_exception(e)
        print("ConnectionError 處理:")
        print(response.get_data(as_text=True))
    
    print()


def main():
    """主測試函數"""
    print("開始測試錯誤處理系統...")
    print("=" * 50)
    
    try:
        test_error_handler()
        test_config_validation()
        test_api_error()
        test_exception_handling()
        
        print("=" * 50)
        print("✅ 所有測試完成！")
        
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
