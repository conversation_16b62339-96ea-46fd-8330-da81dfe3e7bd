"""
PTT 自動登入系統 - 配置管理模組
"""
import os
from dotenv import load_dotenv

# 載入環境變數
load_dotenv()

class Config:
    """系統配置類別"""
    
    # PTT 連接設定
    PTT_USERNAME = os.getenv('PTT_USERNAME', '')
    PTT_PASSWORD = os.getenv('PTT_PASSWORD', '')
    PTT_HOST = os.getenv('PTT_HOST', 'ptt.cc')
    PTT_PORT = int(os.getenv('PTT_PORT', '23'))
    
    # 系統設定
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    TIMEZONE = os.getenv('TIMEZONE', 'Asia/Taipei')
    POST_SCHEDULE = os.getenv('POST_SCHEDULE', '08:00')
    
    # Flask 設定
    SECRET_KEY = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
    DEBUG = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    
    # 重試設定
    MAX_RETRY_ATTEMPTS = int(os.getenv('MAX_RETRY_ATTEMPTS', '3'))
    RETRY_DELAY = int(os.getenv('RETRY_DELAY', '5'))
    
    # 連接超時設定
    CONNECTION_TIMEOUT = int(os.getenv('CONNECTION_TIMEOUT', '30'))
    LOGIN_TIMEOUT = int(os.getenv('LOGIN_TIMEOUT', '10'))

    # Azure Cosmos DB 設定
    COSMOS_DB_ENDPOINT = os.getenv('COSMOS_DB_ENDPOINT', '')
    COSMOS_DB_KEY = os.getenv('COSMOS_DB_KEY', '')
    COSMOS_DB_DATABASE = os.getenv('COSMOS_DB_DATABASE', 'ptt_graph_db')
    COSMOS_DB_COLLECTION = os.getenv('COSMOS_DB_COLLECTION', 'ptt_graph')

    # 爬蟲設定
    DEFAULT_BOARDS = os.getenv('DEFAULT_BOARDS', 'Test').split(',')
    MAX_POSTS_PER_BOARD = int(os.getenv('MAX_POSTS_PER_BOARD', '50'))
    CRAWL_INTERVAL_HOURS = int(os.getenv('CRAWL_INTERVAL_HOURS', '24'))
    
    @classmethod
    def validate_config(cls):
        """驗證 PTT 配置"""
        errors = []
        warnings = []

        # 預設值定義（用於檢查是否為未設定的預設值）
        DEFAULT_USERNAME = 'your_ptt_username'
        DEFAULT_PASSWORD = 'your_ptt_password'

        # PTT 使用者名稱檢查
        if not cls.PTT_USERNAME or cls.PTT_USERNAME == DEFAULT_USERNAME:
            errors.append({
                'field': 'PTT_USERNAME',
                'message': 'PTT 使用者名稱未設定或使用預設值',
                'suggestion': '請在環境變數中設定實際的 PTT_USERNAME',
                'severity': 'error'
            })
        elif len(cls.PTT_USERNAME) < 3:
            warnings.append({
                'field': 'PTT_USERNAME',
                'message': 'PTT 使用者名稱過短',
                'suggestion': '使用者名稱通常至少3個字符',
                'severity': 'warning'
            })

        # PTT 密碼檢查
        if not cls.PTT_PASSWORD or cls.PTT_PASSWORD == DEFAULT_PASSWORD:
            errors.append({
                'field': 'PTT_PASSWORD',
                'message': 'PTT 密碼未設定或使用預設值',
                'suggestion': '請在環境變數中設定實際的 PTT_PASSWORD',
                'severity': 'error'
            })
        elif len(cls.PTT_PASSWORD) < 6:
            warnings.append({
                'field': 'PTT_PASSWORD',
                'message': 'PTT 密碼可能過短',
                'suggestion': '建議使用較長的密碼以提高安全性',
                'severity': 'warning'
            })

        # 效能相關警告
        if cls.MAX_RETRY_ATTEMPTS > 5:
            warnings.append({
                'field': 'MAX_RETRY_ATTEMPTS',
                'message': f'重試次數設定過高: {cls.MAX_RETRY_ATTEMPTS}',
                'suggestion': '建議設定在 3-5 次之間以避免過長等待',
                'severity': 'warning'
            })

        if cls.CONNECTION_TIMEOUT > 60:
            warnings.append({
                'field': 'CONNECTION_TIMEOUT',
                'message': f'連接超時設定過長: {cls.CONNECTION_TIMEOUT}秒',
                'suggestion': '建議設定在 30-60 秒之間',
                'severity': 'warning'
            })

        return {
            'errors': errors,
            'warnings': warnings,
            'is_valid': len(errors) == 0
        }

    @classmethod
    def validate_cosmos_config(cls):
        """驗證 Cosmos DB 配置"""
        errors = []
        warnings = []

        # 預設值定義（用於檢查是否為未設定的預設值）
        DEFAULT_ENDPOINT_PATTERNS = [
            'wss://your-cosmos-account.gremlin.cosmos.azure.com:443/',
            'https://your-cosmos-account.gremlin.cosmos.azure.com:443/',
            'your-cosmos-endpoint'
        ]
        DEFAULT_KEY_PATTERNS = [
            'your-cosmos-key',
            'your_cosmos_key'
        ]

        # Cosmos DB 端點檢查
        if not cls.COSMOS_DB_ENDPOINT or any(pattern in cls.COSMOS_DB_ENDPOINT for pattern in DEFAULT_ENDPOINT_PATTERNS):
            errors.append({
                'field': 'COSMOS_DB_ENDPOINT',
                'message': 'Cosmos DB 端點未設定或使用預設值',
                'suggestion': '請在環境變數中設定實際的 COSMOS_DB_ENDPOINT (格式: https://xxx.gremlin.cosmos.azure.com:443/)',
                'severity': 'error'
            })
        elif not (cls.COSMOS_DB_ENDPOINT.startswith('https://') or cls.COSMOS_DB_ENDPOINT.startswith('wss://')):
            warnings.append({
                'field': 'COSMOS_DB_ENDPOINT',
                'message': 'Cosmos DB 端點格式可能不正確',
                'suggestion': '端點應以 https:// 或 wss:// 開頭',
                'severity': 'warning'
            })

        # Cosmos DB 金鑰檢查
        if not cls.COSMOS_DB_KEY or any(pattern in cls.COSMOS_DB_KEY for pattern in DEFAULT_KEY_PATTERNS):
            errors.append({
                'field': 'COSMOS_DB_KEY',
                'message': 'Cosmos DB 金鑰未設定或使用預設值',
                'suggestion': '請在環境變數中設定實際的 COSMOS_DB_KEY',
                'severity': 'error'
            })
        elif len(cls.COSMOS_DB_KEY) < 50:
            warnings.append({
                'field': 'COSMOS_DB_KEY',
                'message': 'Cosmos DB 金鑰可能不正確',
                'suggestion': 'Cosmos DB 金鑰通常很長，請確認是否完整',
                'severity': 'warning'
            })

        if not cls.COSMOS_DB_DATABASE:
            errors.append({
                'field': 'COSMOS_DB_DATABASE',
                'message': 'Cosmos DB 資料庫名稱未設定',
                'suggestion': '請在環境變數中設定 COSMOS_DB_DATABASE',
                'severity': 'error'
            })

        if not cls.COSMOS_DB_COLLECTION:
            errors.append({
                'field': 'COSMOS_DB_COLLECTION',
                'message': 'Cosmos DB 集合名稱未設定',
                'suggestion': '請在環境變數中設定 COSMOS_DB_COLLECTION',
                'severity': 'error'
            })

        return {
            'errors': errors,
            'warnings': warnings,
            'is_valid': len(errors) == 0
        }
    
    @classmethod
    def get_config_summary(cls):
        """取得配置摘要（隱藏敏感資訊）"""
        return {
            'PTT_USERNAME': cls.PTT_USERNAME[:3] + '***' if cls.PTT_USERNAME else '未設定',
            'PTT_HOST': cls.PTT_HOST,
            'PTT_PORT': cls.PTT_PORT,
            'LOG_LEVEL': cls.LOG_LEVEL,
            'TIMEZONE': cls.TIMEZONE,
            'POST_SCHEDULE': cls.POST_SCHEDULE,
            'MAX_RETRY_ATTEMPTS': cls.MAX_RETRY_ATTEMPTS,
            'CONNECTION_TIMEOUT': cls.CONNECTION_TIMEOUT,
            'COSMOS_DB_ENDPOINT': cls.COSMOS_DB_ENDPOINT[:20] + '***' if cls.COSMOS_DB_ENDPOINT else '未設定',
            'COSMOS_DB_DATABASE': cls.COSMOS_DB_DATABASE,
            'COSMOS_DB_COLLECTION': cls.COSMOS_DB_COLLECTION,
            'DEFAULT_BOARDS': cls.DEFAULT_BOARDS,
            'MAX_POSTS_PER_BOARD': cls.MAX_POSTS_PER_BOARD,
            'CRAWL_INTERVAL_HOURS': cls.CRAWL_INTERVAL_HOURS,
        }
