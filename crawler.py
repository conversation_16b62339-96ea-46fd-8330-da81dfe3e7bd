"""
PTT 自動登入系統 - 爬蟲與資料結構化模組
"""

import json
from datetime import datetime
from typing import List, Dict, Optional
from ptt_client import PTTClient
from config import Config
from logger import ptt_logger

class PTTCrawler:
    """PTT 爬蟲類別，負責自動爬取指定看板的文章和推文資料"""
    
    def __init__(self):
        self.client = PTTClient()
        self.crawl_results = []
    
    def crawl_boards(self, board_list: List[str], max_posts_per_board: int = 50, target_date: str = None) -> Dict:
        """爬取多個看板的資料

        Args:
            board_list: 看板名稱列表，如 ['Gossiping', 'C_Chat']
            max_posts_per_board: 每個看板最大文章數量
            target_date: 目標日期，格式 'YYYY-MM-DD'，如果指定則只取該日期的文章

        Returns:
            dict: 爬取結果 {
                'success': bool,
                'crawl_time': str,
                'target_date': str (如果有指定),
                'boards': [
                    {
                        'board': str,
                        'posts_count': int,
                        'users': set,  # 發文者和推文者集合
                        'ips': set,    # IP 集合
                        'posts': [...]  # 文章資料
                    }
                ],
                'summary': {
                    'total_posts': int,
                    'total_users': int,
                    'total_ips': int,
                    'boards_count': int
                }
            }
        """
        ptt_logger.log_operation_start(f"開始爬取 {len(board_list)} 個看板")
        
        crawl_result = {
            'success': False,
            'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'target_date': target_date,
            'boards': [],
            'summary': {
                'total_posts': 0,
                'total_users': 0,
                'total_ips': 0,
                'boards_count': 0
            }
        }
        
        try:
            # 登入 PTT
            if not self.client.login():
                ptt_logger.error("PTT 登入失敗，無法進行爬取")
                return crawl_result
            
            ptt_logger.info("PTT 登入成功，開始爬取看板資料")
            
            all_users = set()
            all_ips = set()
            total_posts = 0
            
            # 逐一爬取每個看板
            for board_name in board_list:
                ptt_logger.info(f"正在爬取看板: {board_name}")
                
                board_data = self.crawl_single_board(board_name, max_posts_per_board, target_date)
                if board_data:
                    crawl_result['boards'].append(board_data)
                    
                    # 統計資料
                    total_posts += board_data['posts_count']
                    all_users.update(board_data['users'])
                    all_ips.update(board_data['ips'])
                    
                    ptt_logger.info(f"完成爬取看板 {board_name}: {board_data['posts_count']} 篇文章")
                else:
                    ptt_logger.warning(f"爬取看板 {board_name} 失敗")
            
            # 更新摘要資訊
            crawl_result['summary'] = {
                'total_posts': total_posts,
                'total_users': len(all_users),
                'total_ips': len(all_ips),
                'boards_count': len(crawl_result['boards'])
            }
            
            crawl_result['success'] = len(crawl_result['boards']) > 0
            
            ptt_logger.log_operation_end(f"爬取 {len(board_list)} 個看板", 
                                       crawl_result['success'],
                                       f"成功: {crawl_result['summary']['boards_count']} 個看板, "
                                       f"{crawl_result['summary']['total_posts']} 篇文章")
            
        except Exception as e:
            ptt_logger.log_operation_end(f"爬取 {len(board_list)} 個看板", False, f"錯誤: {e}")
        
        finally:
            # 登出
            self.client.disconnect()
        
        return crawl_result
    
    def crawl_single_board(self, board_name: str, max_posts: int = 50, target_date: str = None) -> Optional[Dict]:
        """爬取單一看板的資料

        Args:
            board_name: 看板名稱
            max_posts: 最大文章數量
            target_date: 目標日期，格式 'YYYY-MM-DD'

        Returns:
            dict: 看板資料或 None
        """
        try:
            # 使用 PTTClient 的爬取功能（包含日期過濾）
            board_data = self.client.crawl_board_data(board_name, max_posts, include_content=True, target_date=target_date)
            
            if not board_data:
                return None
            
            # 提取使用者和 IP 資訊
            users = set()
            ips = set()
            
            for post in board_data['posts']:
                # 發文者
                if post.get('author'):
                    users.add(post['author'])
                
                # 發文者 IP
                if post.get('ip'):
                    ips.add(post['ip'])
                
                # 推文者
                for comment in post.get('comments', []):
                    if comment.get('author'):
                        users.add(comment['author'])
                    if comment.get('ip'):
                        ips.add(comment['ip'])
            
            # 結構化資料
            structured_data = {
                'board': board_name,
                'crawl_time': board_data['crawl_time'],
                'posts_count': len(board_data['posts']),
                'users': users,
                'ips': ips,
                'posts': self._structure_posts_data(board_data['posts'])
            }
            
            return structured_data
            
        except Exception as e:
            ptt_logger.error(f"爬取看板 {board_name} 時發生錯誤: {e}")
            return None
    
    def _structure_posts_data(self, posts: List[Dict]) -> List[Dict]:
        """結構化文章資料，準備寫入圖資料庫
        
        Args:
            posts: 原始文章資料
            
        Returns:
            list: 結構化後的文章資料
        """
        structured_posts = []
        
        for post in posts:
            # 基本文章資訊
            structured_post = {
                'post_id': post.get('aid', ''),
                'title': post.get('title', ''),
                'author': post.get('author', ''),
                'board': post.get('board', ''),
                'date': post.get('date', ''),
                'content': post.get('content', ''),
                'author_ip': post.get('ip', ''),
                'comments': []
            }
            
            # 處理推文
            for comment in post.get('comments', []):
                structured_comment = {
                    'type': comment.get('type', ''),
                    'author': comment.get('author', ''),
                    'content': comment.get('content', ''),
                    'ip': comment.get('ip', ''),
                    'time': comment.get('time', '')
                }
                structured_post['comments'].append(structured_comment)
            
            structured_posts.append(structured_post)
        
        return structured_posts
    
    def generate_graph_data(self, crawl_result: Dict) -> Dict:
        """將爬取結果轉換為圖資料庫格式
        
        Args:
            crawl_result: 爬取結果
            
        Returns:
            dict: 圖資料庫格式的資料 {
                'vertices': {
                    'users': [...],
                    'ips': [...],
                    'posts': [...]
                },
                'edges': {
                    'posted': [...],      # 使用者發文
                    'commented': [...],   # 使用者推文
                    'used_ip': [...],     # 使用者使用IP
                    'from_ip': [...]      # 文章來自IP
                }
            }
        """
        graph_data = {
            'vertices': {
                'users': [],
                'ips': [],
                'posts': []
            },
            'edges': {
                'posted': [],
                'commented': [],
                'used_ip': [],
                'from_ip': []
            }
        }
        
        # 收集所有使用者和 IP
        all_users = set()
        all_ips = set()
        
        for board in crawl_result['boards']:
            all_users.update(board['users'])
            all_ips.update(board['ips'])
        
        # 建立頂點
        for user in all_users:
            graph_data['vertices']['users'].append({
                'id': user,
                'type': 'user',
                'properties': {
                    'username': user,
                    'first_seen': crawl_result['crawl_time']
                }
            })
        
        for ip in all_ips:
            graph_data['vertices']['ips'].append({
                'id': ip,
                'type': 'ip',
                'properties': {
                    'address': ip,
                    'first_seen': crawl_result['crawl_time']
                }
            })
        
        # 處理文章和建立邊
        for board in crawl_result['boards']:
            for post in board['posts']:
                post_id = f"{board['board']}.{post['post_id']}"
                
                # 文章頂點
                graph_data['vertices']['posts'].append({
                    'id': post_id,
                    'type': 'post',
                    'properties': {
                        'title': post['title'],
                        'board': board['board'],
                        'date': post['date'],
                        'content_length': len(post['content'])
                    }
                })
                
                # 發文關係
                if post['author']:
                    graph_data['edges']['posted'].append({
                        'from': post['author'],
                        'to': post_id,
                        'type': 'posted',
                        'properties': {
                            'date': post['date'],
                            'board': board['board']
                        }
                    })
                
                # 發文者 IP 關係
                if post['author_ip']:
                    graph_data['edges']['used_ip'].append({
                        'from': post['author'],
                        'to': post['author_ip'],
                        'type': 'used_ip',
                        'properties': {
                            'date': post['date'],
                            'action': 'post'
                        }
                    })
                    
                    graph_data['edges']['from_ip'].append({
                        'from': post_id,
                        'to': post['author_ip'],
                        'type': 'from_ip',
                        'properties': {
                            'date': post['date']
                        }
                    })
                
                # 推文關係
                for comment in post['comments']:
                    if comment['author']:
                        graph_data['edges']['commented'].append({
                            'from': comment['author'],
                            'to': post_id,
                            'type': 'commented',
                            'properties': {
                                'comment_type': comment['type'],
                                'time': comment['time'],
                                'content': comment['content'][:100]  # 限制長度
                            }
                        })
                        
                        # 推文者 IP 關係
                        if comment['ip']:
                            graph_data['edges']['used_ip'].append({
                                'from': comment['author'],
                                'to': comment['ip'],
                                'type': 'used_ip',
                                'properties': {
                                    'date': comment['time'],
                                    'action': 'comment'
                                }
                            })
        
        return graph_data
    
    def save_crawl_result(self, crawl_result: Dict, filename: Optional[str] = None) -> str:
        """儲存爬取結果為 JSON 檔案
        
        Args:
            crawl_result: 爬取結果
            filename: 檔案名稱，如果不提供則自動生成
            
        Returns:
            str: 儲存的檔案路徑
        """
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"ptt_crawl_result_{timestamp}.json"
        
        # 將 set 轉換為 list 以便 JSON 序列化
        serializable_result = self._make_json_serializable(crawl_result)
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(serializable_result, f, ensure_ascii=False, indent=2)
            
            ptt_logger.info(f"爬取結果已儲存至: {filename}")
            return filename
            
        except Exception as e:
            ptt_logger.error(f"儲存爬取結果失敗: {e}")
            raise
    
    def _make_json_serializable(self, obj):
        """將物件轉換為可 JSON 序列化的格式"""
        if isinstance(obj, set):
            return list(obj)
        elif isinstance(obj, dict):
            return {key: self._make_json_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._make_json_serializable(item) for item in obj]
        else:
            return obj
