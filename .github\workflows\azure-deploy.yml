name: Deploy to Azure Web App

on:
  push:
    branches:
      - main
  workflow_dispatch:

env:
  AZURE_WEBAPP_NAME: TwLotteryBot
  AZURE_RESOURCE_GROUP: TwLotteryBot_group
  PYTHON_VERSION: '3.12'

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install backend dependencies
        run: |
          pip install --upgrade pip
          pip install -r requirements.txt

      - name: Build frontend
        run: |
          cd frontend
          npm ci
          npm run build
          cd ..

      - name: Zip source code for deployment
        run: |
          zip -r release.zip . \
            -x "*.git*" "*__pycache__*" "*.pyc" "*.md" ".env" \
            "frontend/node_modules/*" "venv/*" "venv/**"

      - name: Upload artifact for deploy job
        uses: actions/upload-artifact@v4
        with:
          name: python-app
          path: release.zip

  deploy:
    runs-on: ubuntu-latest
    needs: build
    environment:
      name: 'Production'
      url: https://${{ env.AZURE_WEBAPP_NAME }}.azurewebsites.net

    steps:
      - name: Download build artifact
        uses: actions/download-artifact@v4
        with:
          name: python-app

      - name: Unzip artifact
        run: unzip release.zip

      - name: Azure Login
        uses: azure/login@v1
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}

      - name: Deploy to Azure Web App
        uses: azure/webapps-deploy@v2
        with:
          app-name: ${{ env.AZURE_WEBAPP_NAME }}
          package: .

      - name: Health check
        run: |
          sleep 20
          curl -f https://${{ env.AZURE_WEBAPP_NAME }}.azurewebsites.net/ || echo "❌ Health check failed"
