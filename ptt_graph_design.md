# 🧩 PTT 人物關係圖 - Graph DB 設計（核心設計部分）

## 節點定義（Vertices）

| Label     | Properties                               | 說明                         |
|-----------|------------------------------------------|------------------------------|
| `User`    | `userId`, `username`, `nick`, `karma`, `lastSeen` | 使用者帳號資訊               |
| `Post`    | `postId`, `board`, `title`, `timestamp`  | 一篇發文                     |
| `Comment` | `commentId`, `content`, `timestamp`      | 一則留言                     |
| `IP`      | `ipAddress`                              | 使用者登入過的 IP 地址       |

## 邊定義（Edges）

| Edge Label        | From → To          | 說明                                         |
|-------------------|--------------------|----------------------------------------------|
| `POSTED`          | `User → Post`      | 使用者發文                                   |
| `COMMENTED`       | `User → Comment`   | 使用者寫了一則留言                           |
| `REPLIED_TO`      | `Comment → Comment`| 留言回覆另一則留言                           |
| `ON_POST`         | `Comment → Post`   | 留言所屬的文章                               |
| `SAME_IP`         | `User → IP`        | 使用者登入過的 IP                            |
| `MENTIONED`       | `User → User`      | 使用者留言中提及另一位使用者（例如推文@）    |
| `INTERACTED_WITH` | `User → User`      | 在同一篇文章下出現互動（回文、推文等）       |
| `REPOSTED`        | `User → Post`      | 使用者轉貼他人文章（可選）                   |