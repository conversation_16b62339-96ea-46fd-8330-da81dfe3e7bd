#!/usr/bin/env python3
"""
[?]
"""

import subprocess
import sys
import time
from datetime import datetime

def run_test(test_file, test_name):
    """[?]"""
    print(f"\n{'='*60}")
    print(f"[?]: {test_name}")
    print(f"[?]: {test_file}")
    print(f"[?]: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print('='*60)
    
    try:
        start_time = time.time()
        result = subprocess.run([sys.executable, test_file], 
                              capture_output=True, 
                              text=True, 
                              timeout=120)
        end_time = time.time()
        
        duration = end_time - start_time
        
        if result.returncode == 0:
            print(f"[OK] {test_name} [?] ({duration:.2f}[?])")
            if result.stdout:
                print("[?]:")
                print(result.stdout)
            return True
        else:
            print(f"[FAIL] {test_name} [?] ({duration:.2f}[?])")
            if result.stdout:
                print("[?]:")
                print(result.stdout)
            if result.stderr:
                print("[?]:")
                print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print(f"[FAIL] {test_name} [?] ([?]120[?])")
        return False
    except Exception as e:
        print(f"[FAIL] {test_name} [?]: {e}")
        return False

def main():
    """[?]"""
    print("PTT [?] - [?]")
    print("="*60)
    print(f"[?]: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # [?]
    tests = [
        # [?]
        ("deploy_check.py", "[?]"),
        
        # [?]

        ("test_crawler.py", "[?]"),
        ("test_graph_writer.py", "[?]"),
        ("test_graph_query.py", "[?]"),
        ("test_scheduler.py", "[?]"),
        ("test_api.py", "API [?]"),
        
        # [?]
        ("test_cosmos_connection.py", "Cosmos DB [?]"),
    ]
    
    # [?]
    passed = 0
    failed = 0
    skipped = 0
    
    results = []
    
    for test_file, test_name in tests:
        try:
            # [?]
            import os
            if not os.path.exists(test_file):
                print(f"[WARN] [?] {test_name}: [?] {test_file} [?]")
                skipped += 1
                results.append((test_name, "SKIPPED", "[?]"))
                continue
            
            # [?]
            if run_test(test_file, test_name):
                passed += 1
                results.append((test_name, "PASSED", ""))
            else:
                failed += 1
                results.append((test_name, "FAILED", ""))
                
        except KeyboardInterrupt:
            print("\n\n[WARN] [?]")
            break
        except Exception as e:
            print(f"\n[FAIL] [?] {test_name} [?]: {e}")
            failed += 1
            results.append((test_name, "ERROR", str(e)))
    
    # [?]
    print("\n" + "="*60)
    print("[?]")
    print("="*60)
    
    total = passed + failed + skipped
    print(f"[?]: {total}")
    print(f"[?]: {passed}")
    print(f"[?]: {failed}")
    print(f"[?]: {skipped}")
    print(f"[?]: {(passed/total*100):.1f}%" if total > 0 else "N/A")
    
    print("\n[?]:")
    print("-" * 60)
    for test_name, status, note in results:
        status_icon = {
            "PASSED": "[OK]",
            "FAILED": "[FAIL]", 
            "SKIPPED": "[WARN]",
            "ERROR": "[CRASH]"
        }.get(status, "?")
        
        note_text = f" ({note})" if note else ""
        print(f"{status_icon} {test_name:<25} {status}{note_text}")
    
    # [?]
    print("\n" + "="*60)
    print("[?]")
    print("="*60)
    
    if failed == 0 and skipped == 0:
        print("[SUCCESS] [?]")
        print("\n[?]:")
        print("1. [?] Git [?]")
        print("2. [?] Azure Web App [?]")
        print("3. [?]")
        print("4. [?]")
        
    elif failed == 0:
        print("[OK] [?]")
        if skipped > 0:
            print(f"[WARN] [?] {skipped} [?]")
        print("\n[?]:")
        print("1. [?]")
        print("2. [?]")
        print("3. [?]")
        
    else:
        print(f"[ERROR] [?] {failed} [?]")
        print("\n[?]:")
        print("1. [?]")
        print("2. [?]")
        print("3. [?]")
        print("4. [?]")
    
    # [?]
    report_filename = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    try:
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write("PTT [?] - [?]\n")
            f.write("="*50 + "\n")
            f.write(f"[?]: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"[?]: {total}\n")
            f.write(f"[?]: {passed}\n")
            f.write(f"[?]: {failed}\n")
            f.write(f"[?]: {skipped}\n")
            f.write(f"[?]: {(passed/total*100):.1f}%\n" if total > 0 else "[?]: N/A\n")
            f.write("\n[?]:\n")
            f.write("-" * 50 + "\n")
            for test_name, status, note in results:
                note_text = f" ({note})" if note else ""
                f.write(f"{test_name:<25} {status}{note_text}\n")
        
        print(f"\n[FILE] [?]: {report_filename}")
        
    except Exception as e:
        print(f"\n[WARN] [?]: {e}")
    
    print(f"\n[?]: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # [?]
    if failed > 0:
        return 1
    elif skipped > 0:
        return 2  # [?]
    else:
        return 0  # [?]

if __name__ == "__main__":
    exit(main())
