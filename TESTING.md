# PTT 圖形分析系統 - 測試指南

## 測試概覽

本專案包含完整的測試套件，涵蓋所有主要功能模組。測試分為單元測試、整合測試和端到端測試。

## 測試檔案說明

### 核心功能測試

| 測試檔案 | 測試範圍 | 說明 |
|---------|---------|------|
| `test_ptt_client.py` | PTT 客戶端 | 測試 PTT 連接、登入、爬文功能 |
| `test_crawler.py` | 爬蟲模組 | 測試資料爬取和結構化功能 |
| `test_graph_writer.py` | 圖形寫入 | 測試 Cosmos DB 寫入功能 |
| `test_graph_query.py` | 圖形查詢 | 測試圖形查詢和分析功能 |
| `test_scheduler.py` | 任務調度 | 測試定時任務和排程功能 |
| `test_api.py` | API 介面 | 測試 REST API 端點 |

### 輔助測試

| 測試檔案 | 測試範圍 | 說明 |
|---------|---------|------|
| `test_cosmos_connection.py` | Cosmos DB 連接 | 測試資料庫連接和配置 |
| `deploy_check.py` | 部署檢查 | 部署前的完整性檢查 |

## 測試執行

### 1. 環境準備

確保已設定必要的環境變數：

```bash
# 檢查配置
python deploy_check.py
```

### 2. 單獨執行測試

```bash
# PTT 客戶端測試
python test_ptt_client.py

# 爬蟲功能測試
python test_crawler.py

# 圖形資料庫測試
python test_graph_writer.py
python test_graph_query.py

# API 測試
python test_api.py

# 調度器測試
python test_scheduler.py
```

### 3. 批次執行測試

```bash
# 執行所有測試
python -m pytest tests/ -v

# 或使用自訂腳本
python run_all_tests.py
```

## 測試結果解讀

### 成功指標

- ✓ 表示測試通過
- 🎉 表示所有測試通過
- ⚠️ 表示警告（通常是配置問題）

### 失敗處理

- ✗ 表示測試失敗
- 檢查錯誤訊息和建議的解決方案
- 確認環境變數和依賴項設定

## 測試場景

### 1. PTT 客戶端測試

**測試場景**：
- PTT 連接建立
- 帳號登入驗證
- 看板文章爬取
- 推文資料擷取

**預期結果**：
- 成功連接 PTT 伺服器
- 正確登入指定帳號
- 取得文章列表和內容
- 解析推文資料

### 2. 爬蟲模組測試

**測試場景**：
- 單一看板爬取
- 多看板批次爬取
- 資料結構化處理
- JSON 序列化

**預期結果**：
- 成功爬取指定看板
- 正確解析文章和推文
- 生成結構化資料
- 儲存為 JSON 格式

### 3. 圖形資料庫測試

**測試場景**：
- Cosmos DB 連接
- 頂點和邊的建立
- 資料查詢和統計
- 錯誤處理

**預期結果**：
- 成功連接 Cosmos DB
- 正確寫入圖形資料
- 查詢返回預期結果
- 適當的錯誤處理

### 4. API 測試

**測試場景**：
- REST API 端點
- 參數驗證
- 錯誤處理
- CORS 支援

**預期結果**：
- API 端點正常回應
- 正確驗證參數
- 適當的錯誤訊息
- 支援跨域請求

### 5. 調度器測試

**測試場景**：
- 任務排程建立
- 定時執行觸發
- 手動任務執行
- 狀態監控

**預期結果**：
- 成功建立排程任務
- 按時執行爬文任務
- 手動觸發正常運作
- 狀態資訊準確

## 測試資料

### 模擬資料

測試使用模擬資料，避免對 PTT 造成負擔：

```json
{
  "mock_user": "test_user",
  "mock_posts": [
    {
      "title": "測試文章",
      "author": "test_user",
      "board": "Test"
    }
  ],
  "mock_comments": [
    {
      "type": "推",
      "author": "commenter",
      "content": "測試推文"
    }
  ]
}
```

### 測試看板

建議使用以下看板進行測試：
- `Test`：測試專用看板
- 避免使用熱門看板以減少系統負擔

## 效能測試

### 記憶體使用

```bash
# 監控記憶體使用
python -m memory_profiler test_crawler.py
```

### 執行時間

```bash
# 測試執行時間
python -m cProfile test_api.py
```

## 持續整合

### GitHub Actions

專案包含 GitHub Actions 工作流程：

```yaml
# .github/workflows/test.yml
name: Run Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.13'
      - name: Install dependencies
        run: pip install -r requirements.txt
      - name: Run tests
        run: python -m pytest tests/ -v
```

## 測試最佳實踐

### 1. 測試隔離

- 每個測試獨立執行
- 不依賴外部狀態
- 使用模擬資料

### 2. 錯誤處理

- 測試正常和異常情況
- 驗證錯誤訊息
- 確保適當的回復機制

### 3. 效能考量

- 避免過度頻繁的請求
- 使用適當的延遲
- 監控資源使用

### 4. 安全性

- 不在測試中暴露敏感資訊
- 使用測試專用的帳號和資源
- 定期更新測試憑證

## 故障排除

### 常見問題

1. **PTT 連接失敗**
   - 檢查網路連接
   - 確認帳號密碼
   - 檢查 PTT 伺服器狀態

2. **Cosmos DB 連接失敗**
   - 驗證端點和金鑰
   - 檢查防火牆設定
   - 確認資料庫和集合存在

3. **API 測試失敗**
   - 確認 Flask 應用程式啟動
   - 檢查路由註冊
   - 驗證請求格式

### 除錯技巧

```bash
# 啟用詳細日誌
export LOG_LEVEL=DEBUG
python test_crawler.py

# 使用 Python 除錯器
python -m pdb test_api.py

# 檢查網路連接
curl -v http://localhost:5000/api/system/health
```

## 測試報告

測試完成後會生成以下報告：

- 測試覆蓋率報告
- 效能分析報告
- 錯誤日誌摘要
- 建議改進事項

執行 `deploy_check.py` 可獲得完整的系統健康檢查報告。
