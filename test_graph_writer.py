#!/usr/bin/env python3
"""
[?]
"""

import json
from graph_writer import GraphWriter
from config import Config

def test_graph_writer_basic():
    """[?]"""
    print("=== [?] ===")
    
    try:
        # [?]
        writer = GraphWriter()
        print("[OK] [?]")
        
        # [?]
        print(f"   [?]: {'[?]' if writer.is_connected else '[?]'}")
        
        # [?] Cosmos DB [?]
        cosmos_errors = Config.validate_cosmos_config()
        if cosmos_errors:
            print("[WARN] Cosmos DB [?]:")
            for error in cosmos_errors:
                print(f"     - {error}")
            print("   [?]")
            return True  # [?]
        
        # [?]
        if writer.test_connection():
            print("[OK] Cosmos DB [?]")
        else:
            print("[FAIL] Cosmos DB [?]")
            return False
        
        return True
        
    except Exception as e:
        print(f"[FAIL] [?]: {e}")
        return False

def test_mock_graph_data():
    """[?]"""
    print("\n=== [?] ===")
    
    try:
        writer = GraphWriter()
        
        # [?]
        mock_graph_data = {
            'vertices': {
                'users': [
                    {
                        'id': 'test_user1',
                        'type': 'user',
                        'properties': {
                            'username': 'test_user1',
                            'first_seen': '2024-01-01T00:00:00'
                        }
                    },
                    {
                        'id': 'test_user2',
                        'type': 'user',
                        'properties': {
                            'username': 'test_user2',
                            'first_seen': '2024-01-01T00:00:00'
                        }
                    }
                ],
                'ips': [
                    {
                        'id': '*************',
                        'type': 'ip',
                        'properties': {
                            'address': '*************',
                            'first_seen': '2024-01-01T00:00:00'
                        }
                    }
                ],
                'posts': [
                    {
                        'id': 'Test.M.1234567890',
                        'type': 'post',
                        'properties': {
                            'title': '[?]',
                            'board': 'Test',
                            'date': '2024-01-01',
                            'content_length': 100
                        }
                    }
                ]
            },
            'edges': {
                'posted': [
                    {
                        'from': 'test_user1',
                        'to': 'Test.M.1234567890',
                        'type': 'posted',
                        'properties': {
                            'date': '2024-01-01',
                            'board': 'Test'
                        }
                    }
                ],
                'commented': [
                    {
                        'from': 'test_user2',
                        'to': 'Test.M.1234567890',
                        'type': 'commented',
                        'properties': {
                            'comment_type': '[?]',
                            'time': '2024-01-01T12:00:00',
                            'content': '[?]'
                        }
                    }
                ],
                'used_ip': [
                    {
                        'from': 'test_user1',
                        'to': '*************',
                        'type': 'used_ip',
                        'properties': {
                            'date': '2024-01-01',
                            'action': 'post'
                        }
                    }
                ],
                'from_ip': [
                    {
                        'from': 'Test.M.1234567890',
                        'to': '*************',
                        'type': 'from_ip',
                        'properties': {
                            'date': '2024-01-01'
                        }
                    }
                ]
            }
        }
        
        print("[OK] [?]")
        print(f"   [?]: {len(mock_graph_data['vertices']['users'])}")
        print(f"   IP: {len(mock_graph_data['vertices']['ips'])}")
        print(f"   [?]: {len(mock_graph_data['vertices']['posts'])}")
        print(f"   [?]: {sum(len(edges) for edges in mock_graph_data['edges'].values())}")
        
        # [?]
        with open("mock_graph_data.json", 'w', encoding='utf-8') as f:
            json.dump(mock_graph_data, f, ensure_ascii=False, indent=2)
        print("[OK] [?] mock_graph_data.json")
        
        # [?] Cosmos DB [?]
        if writer.is_connected:
            print("\n2. [?] Cosmos DB...")
            if writer.write_graph_data(mock_graph_data):
                print("[OK] [?]")
                
                # [?]
                stats = writer.get_graph_stats()
                if stats:
                    print("   [?]:")
                    print(f"     [?]: {stats['vertices']['users']}")
                    print(f"     IP [?]: {stats['vertices']['ips']}")
                    print(f"     [?]: {stats['vertices']['posts']}")
                    print(f"     [?]: {stats['edges']['posted']}")
                    print(f"     [?]: {stats['edges']['commented']}")
            else:
                print("[FAIL] [?]")
                return False
        else:
            print("[WARN] [?] Cosmos DB[?]")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] [?]: {e}")
        return False

def test_graph_stats():
    """[?]"""
    print("\n=== [?] ===")
    
    try:
        writer = GraphWriter()
        
        if not writer.is_connected:
            print("[WARN] [?] Cosmos DB[?]")
            return True
        
        print("1. [?]...")
        stats = writer.get_graph_stats()
        
        if stats:
            print("[OK] [?]")
            print(f"   [?]: {stats['updated_at']}")
            print("   [?]:")
            for vertex_type, count in stats['vertices'].items():
                print(f"     {vertex_type}: {count}")
            print("   [?]:")
            for edge_type, count in stats['edges'].items():
                print(f"     {edge_type}: {count}")
        else:
            print("[FAIL] [?]")
            return False
        
        return True
        
    except Exception as e:
        print(f"[FAIL] [?]: {e}")
        return False

def main():
    """[?]"""
    print("[?]")
    print("=" * 50)
    
    # [?]
    config_summary = Config.get_config_summary()
    print("[?]:")
    print(f"  Cosmos DB [?]: {config_summary['COSMOS_DB_ENDPOINT']}")
    print(f"  [?]: {config_summary['COSMOS_DB_DATABASE']}")
    print(f"  [?]: {config_summary['COSMOS_DB_COLLECTION']}")
    print()
    
    # [?]
    tests = [
        ("[?]", test_graph_writer_basic),
        ("[?]", test_mock_graph_data),
        ("[?]", test_graph_stats),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"[OK] {test_name} [?]")
            else:
                print(f"[FAIL] {test_name} [?]")
        except Exception as e:
            print(f"[FAIL] {test_name} [?]: {e}")
    
    print("\n" + "=" * 50)
    print(f"[?]: {passed}/{total} [?]")
    
    if passed == total:
        print("[SUCCESS] [?]")
        return 0
    else:
        print("[?] [?] Cosmos DB [?]")
        return 1

if __name__ == "__main__":
    exit(main())
