<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端API測試</title>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            white-space: pre-wrap;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <h1>前端API測試</h1>
    
    <div class="test-section">
        <h3>系統狀態API測試</h3>
        <button onclick="testSystemStatus()">測試系統狀態</button>
        <div id="status-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>錯誤處理測試</h3>
        <button onclick="testErrorHandling()">測試錯誤處理</button>
        <button onclick="testInvalidAPI()">測試無效API</button>
        <div id="error-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>SweetAlert2測試</h3>
        <button onclick="testSweetAlert()">測試成功提示</button>
        <button onclick="testErrorAlert()">測試錯誤提示</button>
        <button onclick="testWarningAlert()">測試警告提示</button>
    </div>

    <script>
        // 簡化的錯誤處理器
        class SimpleErrorHandler {
            static showSuccess(message) {
                Swal.fire({
                    title: '成功',
                    text: message,
                    icon: 'success',
                    timer: 3000,
                    showConfirmButton: false
                });
            }
            
            static showError(message) {
                Swal.fire({
                    title: '錯誤',
                    text: message,
                    icon: 'error',
                    confirmButtonText: '確定'
                });
            }
            
            static showWarning(message) {
                Swal.fire({
                    title: '警告',
                    text: message,
                    icon: 'warning',
                    confirmButtonText: '確定'
                });
            }
        }

        // 測試系統狀態API
        async function testSystemStatus() {
            const resultDiv = document.getElementById('status-result');
            resultDiv.textContent = '載入中...';
            
            try {
                const response = await fetch('/api/system/status');
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `系統狀態: ${data.data.system.status}\n健康度: ${data.data.system.health_percentage}%\n\nPTT配置: ${data.data.configuration.ptt_configured ? '正常' : '有問題'}\nCosmos配置: ${data.data.configuration.cosmos_configured ? '正常' : '有問題'}\n\n配置錯誤:\n${JSON.stringify(data.data.configuration.ptt_config.errors, null, 2)}`;
                    
                    SimpleErrorHandler.showSuccess('系統狀態載入成功！');
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `錯誤: ${data.error}`;
                    SimpleErrorHandler.showError('載入系統狀態失敗');
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `網路錯誤: ${error.message}`;
                SimpleErrorHandler.showError(`網路錯誤: ${error.message}`);
            }
        }

        // 測試錯誤處理
        async function testErrorHandling() {
            const resultDiv = document.getElementById('error-result');
            resultDiv.textContent = '測試錯誤處理...';
            
            try {
                // 測試缺少參數的API
                const response = await fetch('/api/ip-users');
                const data = await response.json();
                
                resultDiv.className = 'result';
                resultDiv.textContent = `API回應:\n${JSON.stringify(data, null, 2)}`;
                
                if (!data.success) {
                    SimpleErrorHandler.showError(`API錯誤: ${data.error.message}`);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `錯誤: ${error.message}`;
            }
        }

        // 測試無效API
        async function testInvalidAPI() {
            const resultDiv = document.getElementById('error-result');
            resultDiv.textContent = '測試無效API...';
            
            try {
                const response = await fetch('/api/invalid-endpoint');
                const data = await response.json();
                
                resultDiv.className = 'result';
                resultDiv.textContent = `API回應:\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `錯誤: ${error.message}`;
                SimpleErrorHandler.showError(`無效API測試: ${error.message}`);
            }
        }

        // 測試SweetAlert2
        function testSweetAlert() {
            SimpleErrorHandler.showSuccess('這是一個成功訊息測試！');
        }

        function testErrorAlert() {
            SimpleErrorHandler.showError('這是一個錯誤訊息測試！');
        }

        function testWarningAlert() {
            SimpleErrorHandler.showWarning('這是一個警告訊息測試！');
        }

        // 頁面載入時自動測試
        window.onload = function() {
            console.log('前端API測試頁面已載入');
            console.log('後端API地址: /api/system/status');
        };
    </script>
</body>
</html>
