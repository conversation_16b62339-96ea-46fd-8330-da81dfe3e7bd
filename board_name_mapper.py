#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PTT 看板名稱對照和自動修正
"""

# PTT 熱門看板的正確大小寫對照表
BOARD_NAME_MAPPING = {
    # 小寫 -> 正確大小寫
    'stock': 'Stock',
    'gossiping': 'Gossiping',
    'baseball': 'Baseball',
    'nba': 'NBA',
    'movie': 'movie',
    'beauty': 'Beauty',
    'joke': 'joke',
    'tech_job': 'Tech_Job',
    'soft_job': 'Soft_Job',
    'car': 'car',
    'mobilesales': 'MobileSales',
    'pc_shopping': 'PC_Shopping',
    'lifeismoney': 'Lifeismoney',
    'marvel': 'Marvel',
    'lol': 'LoL',
    'steam': 'Steam',
    'c_chat': 'C_Chat',
    'wow': 'WOW',
    'pokemon': 'Pokemon',
    'food': 'Food',
    'cookclub': 'cookclub',
    'travel': 'Travel',
    'japan_travel': 'Japan_Travel',
    'korea_travel': 'Korea_Travel',
    'fitness': 'FITNESS',
    'musclebeach': 'MuscleBeach',
    'biker': 'biker',
    'car_pool': 'car-pool',
    'creditcard': 'creditcard',
    'insurance': 'Insurance',
    'tax': 'Tax',
    'salary': 'Salary',
    'job': 'job',
    'part_time': 'part-time',
    'studyabroad': 'studyabroad',
    'graduate': 'graduate',
    'examination': 'Examination',
    'toberich': 'TobeRich',
    'home_sale': 'home-sale',
    'rent_tao': 'rent_tao',
    'rent_apart': 'rent_apart',
    'marriage': 'marriage',
    'babymother': 'BabyMother',
    'parentchild': 'parentchild',
    'womentalk': 'WomenTalk',
    'mentalk': 'MenTalk',
    'boy_girl': 'Boy-Girl',
    'sex': 'sex',
    'lesbian': 'lesbian',
    'gay': 'gay',
    'transgender': 'transgender',
    'psychology': 'Psychology',
    'religion': 'Religion',
    'philosophy': 'Philosophy',
    'historia': 'historia',
    'military': 'Military',
    'warfare': 'warfare',
    'aviation': 'Aviation',
    'railway': 'Railway',
    'bus': 'Bus',
    'mrt': 'MRT',
    'taoyuan': 'Taoyuan',
    'hsinchu': 'Hsinchu',
    'taichung': 'TaiChung',
    'tainan': 'Tainan',
    'kaohsiung': 'Kaohsiung',
    'test': 'Test',
    'sysop': 'SYSOP',
    'allpost': 'AllPost',
    'record': 'Record',
    'deleted': 'deleted',
    'security': 'Security',
    'ask': 'ask',
    'newboard': 'NewBoard',
    'suggestion': 'Suggestion',
    'law': 'LAW',
    'lawyer': 'Lawyer',
    'politics': 'Politics',
    'hatepolitics': 'HatePolitics',
    'publicissue': 'PublicIssue',
    'cfb': 'CFB',
    'nfl': 'NFL',
    'tennis': 'Tennis',
    'volleyball': 'Volleyball',
    'badminton': 'Badminton',
    'tabletennis': 'TableTennis',
    'golf': 'Golf',
    'billiards': 'Billiards',
    'bowling': 'Bowling',
    'fishing': 'Fishing',
    'diving': 'Diving',
    'bicycle': 'bicycle',
    'motorbike': 'Motorbike',
    'racing': 'Racing',
    'formula1': 'Formula1',
    'olympics': 'Olympics',
    'sportlottery': 'SportLottery',
    'music': 'Music',
    'guitar': 'guitar',
    'piano': 'Piano',
    'violin': 'Violin',
    'band': 'band',
    'rock': 'Rock',
    'jazz': 'Jazz',
    'classical': 'Classical',
    'hiphop': 'Hip-Hop',
    'electronic': 'Electronic',
    'country': 'Country',
    'blues': 'Blues',
    'folk': 'Folk',
    'world_music': 'World_Music',
    'audio': 'Audio',
    'headphone': 'Headphone',
    'cd_shop': 'CD-Shop',
    'vinyl': 'Vinyl',
    'concert': 'Concert',
    'karaoke': 'KTV',
    'dance': 'Dance',
    'photography': 'Photography',
    'camera': 'DSLR',
    'mobilecamera': 'MobileCamera',
    'art': 'Art',
    'painting': 'Painting',
    'calligraphy': 'Calligraphy',
    'sculpture': 'Sculpture',
    'design': 'Design',
    'architecture': 'Architecture',
    'interior': 'Interior',
    'fashion': 'Fashion',
    'makeup': 'MakeUp',
    'nail': 'Nail',
    'hair': 'Hair',
    'perfume': 'Perfume',
    'watch': 'Watch',
    'jewelry': 'Jewelry',
    'bag': 'Bag',
    'shoe': 'Shoe',
    'clothing': 'Clothing',
    'accessories': 'Accessories'
}

def normalize_board_name(board_name: str) -> str:
    """
    正規化看板名稱，自動修正大小寫
    
    Args:
        board_name: 輸入的看板名稱
        
    Returns:
        str: 修正後的看板名稱
    """
    if not board_name:
        return board_name
    
    # 移除前後空白
    board_name = board_name.strip()
    
    # 如果已經在對照表中，直接返回正確的名稱
    if board_name in BOARD_NAME_MAPPING.values():
        return board_name
    
    # 轉換為小寫後查找對照表
    lower_name = board_name.lower()
    if lower_name in BOARD_NAME_MAPPING:
        return BOARD_NAME_MAPPING[lower_name]
    
    # 如果找不到對照，嘗試一些常見的規則
    
    # 1. 全大寫的縮寫（如 NBA, NFL）
    if board_name.isupper() and len(board_name) <= 5:
        return board_name.upper()
    
    # 2. 首字母大寫的單詞
    if '_' not in board_name and '-' not in board_name:
        return board_name.capitalize()
    
    # 3. 包含底線或連字號的複合詞
    if '_' in board_name:
        parts = board_name.split('_')
        # 檢查是否是 Job 結尾的看板
        if parts[-1].lower() == 'job':
            return '_'.join([part.capitalize() for part in parts[:-1]] + ['Job'])
        else:
            return '_'.join([part.capitalize() for part in parts])
    
    if '-' in board_name:
        parts = board_name.split('-')
        return '-'.join([part.capitalize() for part in parts])
    
    # 預設返回首字母大寫
    return board_name.capitalize()

def get_board_suggestions(partial_name: str, limit: int = 5) -> list:
    """
    根據部分名稱提供看板建議
    
    Args:
        partial_name: 部分看板名稱
        limit: 返回建議數量限制
        
    Returns:
        list: 建議的看板名稱列表
    """
    if not partial_name:
        return []
    
    partial_lower = partial_name.lower()
    suggestions = []
    
    # 精確匹配
    for correct_name in BOARD_NAME_MAPPING.values():
        if correct_name.lower() == partial_lower:
            suggestions.append(correct_name)
    
    # 前綴匹配
    for correct_name in BOARD_NAME_MAPPING.values():
        if correct_name.lower().startswith(partial_lower) and correct_name not in suggestions:
            suggestions.append(correct_name)
    
    # 包含匹配
    for correct_name in BOARD_NAME_MAPPING.values():
        if partial_lower in correct_name.lower() and correct_name not in suggestions:
            suggestions.append(correct_name)
    
    return suggestions[:limit]

def validate_board_name(board_name: str) -> dict:
    """
    驗證看板名稱並提供修正建議
    
    Args:
        board_name: 要驗證的看板名稱
        
    Returns:
        dict: 驗證結果
    """
    result = {
        'original': board_name,
        'normalized': normalize_board_name(board_name),
        'is_corrected': False,
        'suggestions': [],
        'confidence': 'high'
    }
    
    # 檢查是否有修正
    if result['normalized'] != board_name:
        result['is_corrected'] = True
        
        # 如果是從對照表找到的，信心度高
        if board_name.lower() in BOARD_NAME_MAPPING:
            result['confidence'] = 'high'
        else:
            result['confidence'] = 'medium'
            # 提供其他建議
            result['suggestions'] = get_board_suggestions(board_name)
    
    return result

# 常用看板分類
BOARD_CATEGORIES = {
    '熱門': ['Gossiping', 'Stock', 'Baseball', 'NBA', 'movie', 'Beauty'],
    '科技': ['Tech_Job', 'Soft_Job', 'PC_Shopping', 'MobileSales', 'Steam'],
    '生活': ['Lifeismoney', 'Food', 'Travel', 'FITNESS', 'WomenTalk'],
    '投資': ['Stock', 'creditcard', 'Insurance', 'TobeRich', 'Salary'],
    '工作': ['Tech_Job', 'Soft_Job', 'job', 'part-time', 'Salary'],
    '運動': ['Baseball', 'NBA', 'Tennis', 'FITNESS', 'MuscleBeach'],
    '娛樂': ['movie', 'Music', 'C_Chat', 'LoL', 'Steam', 'joke'],
    '測試': ['Test', 'SYSOP', 'ask']
}

def get_popular_boards(category: str = None) -> list:
    """
    取得熱門看板列表
    
    Args:
        category: 看板分類，None 表示全部
        
    Returns:
        list: 看板名稱列表
    """
    if category and category in BOARD_CATEGORIES:
        return BOARD_CATEGORIES[category]
    
    # 返回所有熱門看板
    all_boards = []
    for boards in BOARD_CATEGORIES.values():
        all_boards.extend(boards)
    
    # 去重並排序
    return sorted(list(set(all_boards)))

if __name__ == "__main__":
    # 測試功能
    test_cases = [
        'stock', 'Stock', 'STOCK',
        'gossiping', 'Gossiping', 'GOSSIPING',
        'tech_job', 'Tech_Job', 'TECH_JOB',
        'nba', 'NBA', 'Nba',
        'test', 'Test', 'TEST'
    ]
    
    print("看板名稱正規化測試:")
    print("=" * 50)
    
    for test_name in test_cases:
        result = validate_board_name(test_name)
        status = "✅ 修正" if result['is_corrected'] else "✅ 正確"
        print(f"{test_name:12} -> {result['normalized']:12} ({status})")
    
    print(f"\n熱門看板分類:")
    print("=" * 50)
    for category, boards in BOARD_CATEGORIES.items():
        print(f"{category}: {', '.join(boards[:5])}")
