{% extends "base.html" %}

{% block title %}爬文管理 - PTT 圖形分析系統{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="bi bi-spider"></i> 爬文管理
        </h1>
        <p class="lead">管理 PTT 文章爬取任務，支援指定日期和看板的文章抓取。</p>
    </div>
</div>

<!-- 爬取表單 -->
<div class="search-form">
    <form id="crawlForm">
        <div class="row">
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="boards" class="form-label">看板列表</label>
                    <input type="text" class="form-control" id="boards" name="boards"
                           value="Test" placeholder="用逗號分隔多個看板，例如: Test,Stock" required
                           autocomplete="off">
                    <div class="form-text">
                        建議先從 Test 看板開始測試。系統會自動修正看板名稱大小寫。
                    </div>
                    <!-- 看板建議下拉選單 -->
                    <div id="boardSuggestions" class="dropdown-menu" style="display: none; position: absolute; z-index: 1000; width: 100%;"></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="mb-3">
                    <label for="maxPosts" class="form-label">每板最大文章數</label>
                    <select class="form-select" id="maxPosts" name="maxPosts">
                        <option value="5">5 篇</option>
                        <option value="10" selected>10 篇</option>
                        <option value="20">20 篇</option>
                        <option value="50">50 篇</option>
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="mb-3">
                    <label for="targetDate" class="form-label">目標日期</label>
                    <input type="date" class="form-control" id="targetDate" name="targetDate">
                    <div class="form-text">留空抓取最新文章</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="mb-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-play-circle"></i> 開始爬取
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- 快速操作 -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-lightning"></i> 快速操作
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <div class="d-grid">
                    <button class="btn btn-outline-success" onclick="crawlToday()">
                        <i class="bi bi-calendar-day"></i> 爬取今天文章
                    </button>
                </div>
            </div>
            <div class="col-md-3">
                <div class="d-grid">
                    <button class="btn btn-outline-info" onclick="crawlLatest()">
                        <i class="bi bi-arrow-clockwise"></i> 爬取最新文章
                    </button>
                </div>
            </div>
            <div class="col-md-3">
                <div class="d-grid">
                    <button class="btn btn-outline-warning" onclick="clearDatabase()">
                        <i class="bi bi-trash"></i> 清空資料庫
                    </button>
                </div>
            </div>
            <div class="col-md-3">
                <div class="d-grid">
                    <button class="btn btn-outline-secondary" onclick="checkCrawlStatus()">
                        <i class="bi bi-info-circle"></i> 檢查狀態
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 載入狀態 -->
<div id="loadingSpinner" class="loading-spinner">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">載入中...</span>
    </div>
    <p class="mt-2" id="loadingMessage">正在執行爬取任務...</p>
</div>

<!-- 結果區域 -->
<div id="resultsContainer" style="display: none;">
    <!-- 爬取統計 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="stats-number" id="crawledBoards">0</div>
                    <div class="text-muted">爬取看板數</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="stats-number" id="crawledPosts">0</div>
                    <div class="text-muted">爬取文章數</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="stats-number" id="crawledUsers">0</div>
                    <div class="text-muted">發現使用者數</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="stats-number" id="crawledIps">0</div>
                    <div class="text-muted">發現 IP 數</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 爬取詳情 -->
    <div class="card result-card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-list-ul"></i> 爬取詳情
            </h5>
        </div>
        <div class="card-body">
            <div id="crawlDetails"></div>
        </div>
    </div>
</div>

<!-- 操作結果 -->
<div id="operationResult"></div>

<!-- 爬取歷史 -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-clock-history"></i> 最近爬取記錄
        </h5>
        <button class="btn btn-sm btn-outline-primary" onclick="loadCrawlHistory()">
            <i class="bi bi-arrow-clockwise"></i> 重新整理
        </button>
    </div>
    <div class="card-body">
        <div id="crawlHistory">
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">載入中...</span>
                </div>
                <p class="mt-2">正在載入爬取記錄...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 熱門看板建議
const POPULAR_BOARDS = {
    '熱門': ['Test', 'Stock', 'Baseball', 'NBA', 'movie', 'Beauty'],
    '科技': ['Tech_Job', 'Soft_Job', 'PC_Shopping', 'MobileSales', 'Steam'],
    '生活': ['Lifeismoney', 'Food', 'Travel', 'FITNESS', 'WomenTalk'],
    '投資': ['Stock', 'creditcard', 'Insurance', 'TobeRich', 'Salary'],
    '工作': ['Tech_Job', 'Soft_Job', 'job', 'part-time', 'Salary'],
    '運動': ['Baseball', 'NBA', 'Tennis', 'FITNESS', 'MuscleBeach'],
    '娛樂': ['movie', 'Music', 'C_Chat', 'LoL', 'Steam', 'joke']
};

// 看板名稱對照表（小寫 -> 正確大小寫）
const BOARD_NAME_MAPPING = {
    'stock': 'Stock',
    'gossiping': 'Gossiping',
    'baseball': 'Baseball',
    'nba': 'NBA',
    'movie': 'movie',
    'beauty': 'Beauty',
    'joke': 'joke',
    'tech_job': 'Tech_Job',
    'soft_job': 'Soft_Job',
    'test': 'Test'
};

// 頁面載入時載入爬取歷史
document.addEventListener('DOMContentLoaded', function() {
    loadCrawlHistory();

    // 設定今天的日期為預設值
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('targetDate').value = today;

    // 設定看板輸入框的自動完成功能
    setupBoardAutoComplete();
});

// 表單提交處理
document.getElementById('crawlForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const boards = document.getElementById('boards').value.trim();
    const maxPosts = document.getElementById('maxPosts').value;
    const targetDate = document.getElementById('targetDate').value;
    
    if (!boards) {
        showError('請輸入看板名稱', 'operationResult');
        return;
    }
    
    await startCrawl(boards, maxPosts, targetDate);
});

// 開始爬取
async function startCrawl(boards, maxPosts, targetDate) {
    // 顯示載入狀態
    showLoading('loadingSpinner');
    document.getElementById('loadingMessage').textContent = '正在執行爬取任務...';
    document.getElementById('resultsContainer').style.display = 'none';
    document.getElementById('operationResult').innerHTML = '';
    
    try {
        // 這裡應該呼叫後端的爬取 API
        // 由於目前沒有專門的爬取 API，我們模擬一個爬取過程
        
        showSuccess('爬取任務已啟動，請稍候...', 'operationResult');
        
        // 模擬爬取過程
        await simulateCrawl(boards, maxPosts, targetDate);
        
    } catch (error) {
        showError(`爬取失敗: ${error.message}`, 'operationResult');
    } finally {
        hideLoading('loadingSpinner');
    }
}

// 模擬爬取過程（實際應該呼叫後端 API）
async function simulateCrawl(boards, maxPosts, targetDate) {
    const boardList = boards.split(',').map(b => b.trim());
    
    // 更新載入訊息
    document.getElementById('loadingMessage').textContent = `正在爬取 ${boardList.length} 個看板...`;
    
    // 等待一段時間模擬爬取
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 顯示模擬結果
    displayCrawlResults({
        boards: boardList.length,
        posts: Math.floor(Math.random() * maxPosts * boardList.length),
        users: Math.floor(Math.random() * 50),
        ips: Math.floor(Math.random() * 30),
        details: boardList.map(board => ({
            board: board,
            posts: Math.floor(Math.random() * maxPosts),
            success: Math.random() > 0.1
        }))
    });
}

// 顯示爬取結果
function displayCrawlResults(data) {
    // 更新統計數字
    document.getElementById('crawledBoards').textContent = data.boards;
    document.getElementById('crawledPosts').textContent = data.posts;
    document.getElementById('crawledUsers').textContent = data.users;
    document.getElementById('crawledIps').textContent = data.ips;
    
    // 顯示詳情
    let html = '<div class="table-responsive"><table class="table table-striped">';
    html += '<thead><tr><th>看板</th><th>文章數</th><th>狀態</th></tr></thead><tbody>';
    
    data.details.forEach(detail => {
        const statusClass = detail.success ? 'success' : 'danger';
        const statusText = detail.success ? '成功' : '失敗';
        const statusIcon = detail.success ? 'check-circle' : 'x-circle';
        
        html += `
            <tr>
                <td>${detail.board}</td>
                <td>${detail.posts}</td>
                <td>
                    <span class="badge bg-${statusClass}">
                        <i class="bi bi-${statusIcon}"></i> ${statusText}
                    </span>
                </td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    document.getElementById('crawlDetails').innerHTML = html;
    
    // 顯示結果區域
    document.getElementById('resultsContainer').style.display = 'block';
    
    // 重新載入爬取歷史
    loadCrawlHistory();
}

// 快速操作函數
async function crawlToday() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('targetDate').value = today;
    document.getElementById('boards').value = 'Test';
    document.getElementById('maxPosts').value = '10';
    
    await startCrawl('Test', '10', today);
}

async function crawlLatest() {
    document.getElementById('targetDate').value = '';
    document.getElementById('boards').value = 'Test';
    document.getElementById('maxPosts').value = '5';
    
    await startCrawl('Test', '5', '');
}

async function clearDatabase() {
    if (!confirm('確定要清空資料庫嗎？此操作無法復原！')) {
        return;
    }
    
    const resultContainer = document.getElementById('operationResult');
    resultContainer.innerHTML = `
        <div class="alert alert-warning">
            <i class="bi bi-hourglass-split"></i> 正在清空資料庫...
        </div>
    `;
    
    try {
        // 這裡應該呼叫清空資料庫的 API
        await new Promise(resolve => setTimeout(resolve, 2000)); // 模擬清空過程
        
        resultContainer.innerHTML = `
            <div class="alert alert-success">
                <i class="bi bi-check-circle"></i> 資料庫已清空
            </div>
        `;
        
        // 重置統計數字
        document.getElementById('crawledBoards').textContent = '0';
        document.getElementById('crawledPosts').textContent = '0';
        document.getElementById('crawledUsers').textContent = '0';
        document.getElementById('crawledIps').textContent = '0';
        
    } catch (error) {
        resultContainer.innerHTML = `
            <div class="alert alert-danger">
                <i class="bi bi-x-circle"></i> 清空資料庫失敗: ${error.message}
            </div>
        `;
    }
}

async function checkCrawlStatus() {
    const resultContainer = document.getElementById('operationResult');
    
    try {
        const response = await apiRequest('/api/graph-stats');
        
        if (response.success) {
            const data = response.data;
            const totalVertices = data.vertices.users + data.vertices.ips + data.vertices.posts;
            const totalEdges = data.edges.posted + data.edges.commented + data.edges.used_ip + data.edges.from_ip;
            
            resultContainer.innerHTML = `
                <div class="alert alert-info">
                    <h6><i class="bi bi-info-circle"></i> 目前資料庫狀態</h6>
                    <ul class="mb-0">
                        <li>總頂點數: ${totalVertices}</li>
                        <li>總邊數: ${totalEdges}</li>
                        <li>使用者: ${data.vertices.users}</li>
                        <li>IP 位址: ${data.vertices.ips}</li>
                        <li>文章: ${data.vertices.posts}</li>
                        <li>最後更新: ${new Date(data.updated_at).toLocaleString('zh-TW')}</li>
                    </ul>
                </div>
            `;
        } else {
            throw new Error(response.error || '未知錯誤');
        }
    } catch (error) {
        resultContainer.innerHTML = `
            <div class="alert alert-danger">
                <i class="bi bi-x-circle"></i> 無法取得狀態: ${error.message}
            </div>
        `;
    }
}

// 載入爬取歷史
async function loadCrawlHistory() {
    const container = document.getElementById('crawlHistory');
    
    // 模擬爬取歷史資料
    const historyData = [
        {
            time: new Date(Date.now() - 1000 * 60 * 30).toLocaleString('zh-TW'),
            boards: ['Test'],
            posts: 11,
            status: 'success'
        },
        {
            time: new Date(Date.now() - 1000 * 60 * 60 * 2).toLocaleString('zh-TW'),
            boards: ['Test'],
            posts: 8,
            status: 'success'
        },
        {
            time: new Date(Date.now() - 1000 * 60 * 60 * 24).toLocaleString('zh-TW'),
            boards: ['Gossiping'],
            posts: 0,
            status: 'failed'
        }
    ];
    
    if (historyData.length === 0) {
        container.innerHTML = '<div class="text-muted">暫無爬取記錄</div>';
        return;
    }
    
    let html = '<div class="table-responsive"><table class="table table-sm">';
    html += '<thead><tr><th>時間</th><th>看板</th><th>文章數</th><th>狀態</th></tr></thead><tbody>';
    
    historyData.forEach(record => {
        const statusClass = record.status === 'success' ? 'success' : 'danger';
        const statusText = record.status === 'success' ? '成功' : '失敗';
        const statusIcon = record.status === 'success' ? 'check-circle' : 'x-circle';
        
        html += `
            <tr>
                <td><small>${record.time}</small></td>
                <td>${record.boards.join(', ')}</td>
                <td>${record.posts}</td>
                <td>
                    <span class="badge bg-${statusClass}">
                        <i class="bi bi-${statusIcon}"></i> ${statusText}
                    </span>
                </td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// 設定看板自動完成功能
function setupBoardAutoComplete() {
    const boardsInput = document.getElementById('boards');
    const suggestionsDiv = document.getElementById('boardSuggestions');

    // 監聽輸入事件
    boardsInput.addEventListener('input', function(e) {
        const value = e.target.value;
        const lastBoard = getLastBoardInput(value);

        if (lastBoard.length >= 1) {
            showBoardSuggestions(lastBoard, suggestionsDiv);
        } else {
            hideBoardSuggestions(suggestionsDiv);
        }
    });

    // 監聽焦點事件
    boardsInput.addEventListener('focus', function(e) {
        const value = e.target.value;
        const lastBoard = getLastBoardInput(value);

        if (lastBoard.length >= 1) {
            showBoardSuggestions(lastBoard, suggestionsDiv);
        }
    });

    // 點擊其他地方時隱藏建議
    document.addEventListener('click', function(e) {
        if (!boardsInput.contains(e.target) && !suggestionsDiv.contains(e.target)) {
            hideBoardSuggestions(suggestionsDiv);
        }
    });
}

// 取得最後一個看板輸入
function getLastBoardInput(value) {
    const boards = value.split(',');
    return boards[boards.length - 1].trim();
}

// 顯示看板建議
function showBoardSuggestions(input, suggestionsDiv) {
    const suggestions = getBoardSuggestions(input);

    if (suggestions.length === 0) {
        hideBoardSuggestions(suggestionsDiv);
        return;
    }

    let html = '';

    // 如果有自動修正建議
    const corrected = normalizeBoardName(input);
    if (corrected !== input) {
        html += `<div class="dropdown-item-text bg-light">
            <small><i class="bi bi-magic"></i> 自動修正: <strong>${corrected}</strong></small>
        </div>`;
    }

    // 分類顯示建議
    const categories = Object.keys(POPULAR_BOARDS);
    let hasMatches = false;

    for (const category of categories) {
        const categoryBoards = POPULAR_BOARDS[category];
        const matches = categoryBoards.filter(board =>
            board.toLowerCase().includes(input.toLowerCase())
        );

        if (matches.length > 0) {
            if (hasMatches) html += '<div class="dropdown-divider"></div>';
            html += `<div class="dropdown-item-text"><small class="text-muted">${category}</small></div>`;

            matches.forEach(board => {
                html += `<a class="dropdown-item" href="#" onclick="selectBoard('${board}')">${board}</a>`;
            });

            hasMatches = true;
        }
    }

    suggestionsDiv.innerHTML = html;
    suggestionsDiv.style.display = 'block';
}

// 隱藏看板建議
function hideBoardSuggestions(suggestionsDiv) {
    suggestionsDiv.style.display = 'none';
}

// 取得看板建議
function getBoardSuggestions(input) {
    const allBoards = [];
    Object.values(POPULAR_BOARDS).forEach(boards => {
        allBoards.push(...boards);
    });

    return [...new Set(allBoards)].filter(board =>
        board.toLowerCase().includes(input.toLowerCase())
    ).slice(0, 10);
}

// 正規化看板名稱
function normalizeBoardName(boardName) {
    const lower = boardName.toLowerCase();
    return BOARD_NAME_MAPPING[lower] || boardName;
}

// 選擇看板
function selectBoard(boardName) {
    const boardsInput = document.getElementById('boards');
    const currentValue = boardsInput.value;
    const boards = currentValue.split(',');

    // 替換最後一個看板名稱
    boards[boards.length - 1] = boardName;

    boardsInput.value = boards.join(', ');
    hideBoardSuggestions(document.getElementById('boardSuggestions'));
    boardsInput.focus();
}
</script>
{% endblock %}
