# PTT 圖形分析系統 - 配置指引

## 🔧 **本地開發環境設定**

### 1. 環境變數設定

1. **複製環境變數範本**：
   ```bash
   cp .env.example .env
   ```

2. **編輯 .env 文件**，填入您的實際設定值：

#### PTT 帳號設定
```bash
PTT_USERNAME=your_ptt_username    # 您的PTT帳號
PTT_PASSWORD=your_ptt_password    # 您的PTT密碼
```

#### Azure Cosmos DB 設定
```bash
COSMOS_DB_ENDPOINT=wss://your-cosmos-account.gremlin.cosmos.azure.com:443/
COSMOS_DB_KEY=your_cosmos_db_primary_key
COSMOS_DB_DATABASE=ptt_graph_db
COSMOS_DB_COLLECTION=ptt_graph
```

#### Flask 安全設定
```bash
SECRET_KEY=your-production-secret-key-here    # 請使用強密碼
```

### 2. 如何取得 Azure Cosmos DB 設定值

#### 方法一：Azure Portal
1. 登入 [Azure Portal](https://portal.azure.com)
2. 找到您的 Cosmos DB 帳戶
3. 在左側選單點選「金鑰」
4. 複製以下資訊：
   - **URI** → `COSMOS_DB_ENDPOINT` (將 https 改為 wss，並加上 :443/)
   - **主要金鑰** → `COSMOS_DB_KEY`

#### 方法二：Azure CLI
```bash
# 取得端點
az cosmosdb show --name your-cosmos-account --resource-group your-resource-group --query "documentEndpoint"

# 取得金鑰
az cosmosdb keys list --name your-cosmos-account --resource-group your-resource-group --query "primaryMasterKey"
```

### 3. 建立 Cosmos DB 圖形資料庫

1. **建立資料庫**：
   - 資料庫 ID：`ptt_graph_db`
   - 佈建輸送量：400 RU/s (可調整)

2. **建立圖形集合**：
   - 集合 ID：`ptt_graph`
   - 分割區索引鍵：`/userid` (重要！)

## 🚀 **Azure Web App 部署設定**

### 1. 在 Azure Web App 中設定環境變數

在 Azure Portal 中：
1. 找到您的 Web App
2. 左側選單 → 「組態」
3. 「應用程式設定」標籤
4. 點選「+ 新增應用程式設定」

### 2. 必要的環境變數

| 變數名稱 | 說明 | 範例值 |
|---------|------|--------|
| `PTT_USERNAME` | PTT 帳號 | `your_username` |
| `PTT_PASSWORD` | PTT 密碼 | `your_password` |
| `COSMOS_DB_ENDPOINT` | Cosmos DB 端點 | `wss://xxx.gremlin.cosmos.azure.com:443/` |
| `COSMOS_DB_KEY` | Cosmos DB 金鑰 | `your_primary_key` |
| `SECRET_KEY` | Flask 密鑰 | `your-strong-secret-key` |

### 3. Python 執行環境設定

在「組態」→「一般設定」中：
- **Python 版本**：3.13
- **啟動命令**：`gunicorn --bind=0.0.0.0 --timeout 600 app:app`

## 🔒 **安全性最佳實務**

### 1. 環境變數安全
- ✅ 使用環境變數存儲敏感資訊
- ✅ 不要在程式碼中硬編碼密碼
- ✅ 定期更換密碼和金鑰
- ❌ 不要將 .env 文件提交到版本控制

### 2. Azure Key Vault (進階)
對於生產環境，建議使用 Azure Key Vault：

```python
from azure.keyvault.secrets import SecretClient
from azure.identity import DefaultAzureCredential

credential = DefaultAzureCredential()
client = SecretClient(vault_url="https://your-vault.vault.azure.net/", credential=credential)

ptt_password = client.get_secret("ptt-password").value
cosmos_key = client.get_secret("cosmos-db-key").value
```

### 3. 網路安全
- 設定 Cosmos DB 防火牆規則
- 使用 HTTPS 連接
- 設定適當的 CORS 政策

## 🔍 **設定驗證**

### 1. 本地測試
```bash
# 測試 PTT 連接
python test_ptt_connection.py

# 測試 Cosmos DB 連接
python test_cosmos_connection.py

# 測試完整系統
python app.py
```

### 2. 部署後驗證
```bash
# 健康檢查
curl https://your-app.azurewebsites.net/api/system/health

# 功能測試
curl "https://your-app.azurewebsites.net/api/user-network-simple?username=test&max_depth=2"
```

## 🚨 **常見問題**

### 1. Cosmos DB 連接失敗
- 檢查端點格式：必須是 `wss://` 開頭，`:443/` 結尾
- 確認金鑰正確：從 Azure Portal 重新複製
- 檢查防火牆設定：允許 Azure 服務存取

### 2. PTT 連接問題
- 確認帳號密碼正確
- 檢查網路連接
- 確認 PTT 帳號未被鎖定

### 3. 部署問題
- 檢查 Python 版本設定
- 確認所有環境變數已設定
- 查看 Azure 日誌串流

## 📞 **支援**

如果遇到問題：
1. 檢查系統日誌
2. 確認環境變數設定
3. 測試網路連接
4. 查看 Azure 監控指標
