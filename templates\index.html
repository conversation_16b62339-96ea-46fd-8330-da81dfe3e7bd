<!doctype html>
<html lang="zh-TW">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>PTT 自動登入系統 - 測試介面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-1BmE4kWBq78iYhFldvKuhfTAU6auU8tT94WrHftjDbrCEXSU1oBoqyl2QvZ6jIW3" crossorigin="anonymous">
    <link rel="shortcut icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <style>
        .config-info {
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 20px 0;
        }
        .error-info {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
            padding: 15px;
            margin: 20px 0;
        }
        .test-result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
        }
        .test-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .test-failure {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            display: none;
        }
        .detail-item {
            margin: 5px 0;
            padding: 5px;
            background-color: #f8f9fa;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <!-- 導航列 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">PTT 圖形分析系統</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/">首頁</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/user-analysis">使用者分析</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/ip-analysis">IP 分析</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/post-analysis">文章分析</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/system-status">系統狀態</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/crawl-management">爬文管理</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/api/graph-stats" target="_blank">API 文件</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <main class="container mt-4">
        <div class="row">
            <!-- 左側：快速導航 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">🔍 快速分析</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="/user-analysis" class="btn btn-outline-primary">
                                👥 使用者分析
                            </a>
                            <a href="/ip-analysis" class="btn btn-outline-success">
                                🌐 IP 分析
                            </a>
                            <a href="/post-analysis" class="btn btn-outline-info">
                                📝 文章分析
                            </a>
                            <a href="/system-status" class="btn btn-outline-warning">
                                📊 系統狀態
                            </a>
                            <a href="/crawl-management" class="btn btn-outline-secondary">
                                🕷️ 爬文管理
                            </a>
                        </div>
                    </div>
                </div>

                <!-- API 快速連結 -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="mb-0">🔗 API 快速連結</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-1">
                            <a href="/api/graph-stats" target="_blank" class="btn btn-sm btn-outline-dark">圖形統計</a>
                            <a href="/api/users" target="_blank" class="btn btn-sm btn-outline-dark">使用者列表</a>
                            <a href="/api/ips" target="_blank" class="btn btn-sm btn-outline-dark">IP 列表</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右側：主要內容 -->
            <div class="col-md-8">
                <div class="text-center mb-4">
                    <h1 class="display-5">PTT 圖形分析系統</h1>
                    <p class="lead">分析 PTT 使用者行為和關聯關係</p>
                </div>

                <!-- 配置資訊 -->
                <div class="config-info">
                    <h5><i class="bi bi-gear"></i> 系統配置</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>PTT 使用者:</strong> {{ config.PTT_USERNAME }}</p>
                            <p><strong>PTT 主機:</strong> {{ config.PTT_HOST }}:{{ config.PTT_PORT }}</p>
                            <p><strong>日誌等級:</strong> {{ config.LOG_LEVEL }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>時區:</strong> {{ config.TIMEZONE }}</p>
                            <p><strong>重試次數:</strong> {{ config.MAX_RETRY_ATTEMPTS }}</p>
                            <p><strong>連接超時:</strong> {{ config.CONNECTION_TIMEOUT }}秒</p>
                        </div>
                    </div>
                </div>

                <!-- 配置錯誤警告 -->
                {% if config_errors %}
                <div class="error-info">
                    <h5><i class="bi bi-exclamation-triangle"></i> 配置問題</h5>
                    <ul class="mb-0">
                        {% for error in config_errors %}
                        <li>{{ error }}</li>
                        {% endfor %}
                    </ul>
                </div>
                {% endif %}

                <!-- 測試表單 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">PTT 登入測試</h5>
                    </div>
                    <div class="card-body">
                        <form id="testForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="username" class="form-label">PTT 帳號</label>
                                        <input type="text" class="form-control" id="username" name="username"
                                               placeholder="留空使用環境變數設定">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="password" class="form-label">PTT 密碼</label>
                                        <input type="password" class="form-control" id="password" name="password"
                                               placeholder="留空使用環境變數設定">
                                    </div>
                                </div>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg" id="testBtn">
                                    <span class="btn-text">開始測試登入</span>
                                    <span class="loading">
                                        <span class="spinner-border spinner-border-sm" role="status"></span>
                                        測試中...
                                    </span>
                                </button>
                            </div>
                        </form>

                        <!-- 測試結果 -->
                        <div id="testResult" class="test-result" style="display: none;">
                            <h6>測試結果</h6>
                            <div id="resultContent"></div>
                        </div>
                    </div>
                </div>

                <!-- 系統狀態 -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">系統狀態</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-outline-info" onclick="checkStatus()">檢查狀態</button>
                        <div id="statusResult" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </main>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // PTT 登入測試
        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const testBtn = document.getElementById('testBtn');
            const btnText = testBtn.querySelector('.btn-text');
            const loading = testBtn.querySelector('.loading');
            const resultDiv = document.getElementById('testResult');
            const resultContent = document.getElementById('resultContent');

            // 顯示載入狀態
            btnText.style.display = 'none';
            loading.style.display = 'inline';
            testBtn.disabled = true;
            resultDiv.style.display = 'none';

            // 取得表單資料
            const formData = new FormData(this);

            // 發送測試請求
            fetch('/test-login', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                // 顯示結果
                resultDiv.style.display = 'block';
                resultDiv.className = 'test-result ' + (data.success ? 'test-success' : 'test-failure');

                let html = `
                    <div class="d-flex align-items-center mb-2">
                        <strong>${data.success ? '✓' : '✗'} ${data.message}</strong>
                        <small class="text-muted ms-auto">${data.timestamp}</small>
                    </div>
                `;

                if (data.details && data.details.length > 0) {
                    html += '<div class="details">';
                    data.details.forEach(detail => {
                        html += `<div class="detail-item">${detail}</div>`;
                    });
                    html += '</div>';
                }

                resultContent.innerHTML = html;
            })
            .catch(error => {
                resultDiv.style.display = 'block';
                resultDiv.className = 'test-result test-failure';
                resultContent.innerHTML = `<strong>✗ 請求失敗: ${error.message}</strong>`;
            })
            .finally(() => {
                // 恢復按鈕狀態
                btnText.style.display = 'inline';
                loading.style.display = 'none';
                testBtn.disabled = false;
            });
        });

        // 檢查系統狀態
        function checkStatus() {
            const statusResult = document.getElementById('statusResult');
            statusResult.innerHTML = '<div class="text-muted">檢查中...</div>';

            fetch('/status')
            .then(response => response.json())
            .then(data => {
                let html = '<div class="row">';

                if (data.ptt_client) {
                    html += `
                        <div class="col-md-6">
                            <h6>PTT 客戶端狀態</h6>
                            <p>連接狀態: ${data.ptt_client.connected ? '已連接' : '未連接'}</p>
                            <p>登入狀態: ${data.ptt_client.logged_in ? '已登入' : '未登入'}</p>
                            <p>最後操作: ${data.ptt_client.last_operation || '無'}</p>
                        </div>
                    `;
                }

                html += `
                    <div class="col-md-6">
                        <h6>配置狀態</h6>
                        <p>配置有效: ${data.config_valid ? '是' : '否'}</p>
                        ${data.config_errors && data.config_errors.length > 0 ?
                            '<p class="text-danger">錯誤: ' + data.config_errors.join(', ') + '</p>' :
                            '<p class="text-success">配置正常</p>'
                        }
                    </div>
                `;

                html += '</div>';
                statusResult.innerHTML = html;
            })
            .catch(error => {
                statusResult.innerHTML = `<div class="text-danger">狀態檢查失敗: ${error.message}</div>`;
            });
        }

        // 頁面載入時自動檢查狀態
        document.addEventListener('DOMContentLoaded', function() {
            checkStatus();
        });
    </script>
</body>
</html>