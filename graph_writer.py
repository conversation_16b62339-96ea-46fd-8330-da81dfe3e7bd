"""
PTT 自動登入系統 - 圖形資料庫寫入模組
整合 Azure Cosmos DB Gremlin API
"""

import json
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime
from gremlin_python.driver import client, serializer
from gremlin_python.driver.protocol import GremlinServerError
from config import Config
from logger import ptt_logger

class GraphWriter:
    """圖形資料庫寫入器，負責將 PTT 爬取資料寫入 Azure Cosmos DB"""
    
    def __init__(self):
        self.client = None
        self.is_connected = False
        self._init_connection()
    
    def _init_connection(self):
        """初始化 Cosmos DB Gremlin 連接"""
        try:
            # 從配置取得連接資訊
            endpoint = Config.COSMOS_DB_ENDPOINT
            key = Config.COSMOS_DB_KEY
            database = Config.COSMOS_DB_DATABASE
            collection = Config.COSMOS_DB_COLLECTION
            
            if not all([endpoint, key, database, collection]):
                ptt_logger.warning("Cosmos DB 配置不完整，圖形資料庫功能將無法使用")
                return
            
            # 建立 Gremlin 客戶端
            try:
                # 嘗試使用 aiohttp 傳輸
                from gremlin_python.driver.aiohttp.transport import AiohttpTransport
                transport_factory = lambda: AiohttpTransport()

                # 確保端點格式正確
                if not endpoint.startswith('wss://'):
                    if endpoint.startswith('https://'):
                        endpoint = endpoint.replace('https://', 'wss://')
                    else:
                        endpoint = f'wss://{endpoint}'

                if not endpoint.endswith('/'):
                    endpoint += '/'

                self.client = client.Client(
                    endpoint,
                    'g',
                    username=f"/dbs/{database}/colls/{collection}",
                    password=key,
                    message_serializer=serializer.GraphSONSerializersV2d0(),
                    transport_factory=transport_factory
                )
            except ImportError:
                # 如果沒有 aiohttp，使用預設傳輸
                # 確保端點格式正確
                if not endpoint.startswith('wss://'):
                    if endpoint.startswith('https://'):
                        endpoint = endpoint.replace('https://', 'wss://')
                    else:
                        endpoint = f'wss://{endpoint}'

                if not endpoint.endswith('/'):
                    endpoint += '/'

                self.client = client.Client(
                    endpoint,
                    'g',
                    username=f"/dbs/{database}/colls/{collection}",
                    password=key,
                    message_serializer=serializer.GraphSONSerializersV2d0()
                )
            
            self.is_connected = True
            ptt_logger.info("Cosmos DB Gremlin 連接初始化成功")
            
        except Exception as e:
            ptt_logger.error(f"初始化 Cosmos DB 連接失敗: {e}")
            self.is_connected = False
    
    def test_connection(self) -> bool:
        """測試資料庫連接"""
        if not self.is_connected or not self.client:
            return False
        
        try:
            # 執行簡單查詢測試連接
            result = self.client.submit("g.V().count()").all().result()
            ptt_logger.info(f"Cosmos DB 連接測試成功，目前頂點數: {result[0] if result else 0}")
            return True
            
        except Exception as e:
            ptt_logger.error(f"Cosmos DB 連接測試失敗: {e}")
            return False
    
    def write_graph_data(self, graph_data: Dict, incremental: bool = True) -> bool:
        """將圖形資料寫入 Cosmos DB

        Args:
            graph_data: 由 crawler.generate_graph_data() 生成的圖形資料
            incremental: 是否進行增量更新，預設 True

        Returns:
            bool: 寫入是否成功
        """
        if not self.is_connected:
            ptt_logger.error("未連接到 Cosmos DB，無法寫入資料")
            return False
        
        try:
            ptt_logger.log_operation_start("寫入圖形資料到 Cosmos DB")

            # 寫入頂點
            vertices_result = self._write_vertices(graph_data['vertices'], incremental)
            ptt_logger.info(f"成功寫入 {vertices_result['written']} 個頂點")
            if incremental and vertices_result['updated'] > 0:
                ptt_logger.info(f"更新了 {vertices_result['updated']} 個頂點")

            # 寫入邊
            edges_result = self._write_edges(graph_data['edges'], incremental)
            ptt_logger.info(f"成功寫入 {edges_result['written']} 條邊")
            if incremental and edges_result['new_comments'] > 0:
                ptt_logger.info(f"新增了 {edges_result['new_comments']} 條推文")

            ptt_logger.log_operation_end("寫入圖形資料到 Cosmos DB", True,
                                       f"頂點: {vertices_result['written']}, 邊: {edges_result['written']}")
            return True
            
        except Exception as e:
            ptt_logger.log_operation_end("寫入圖形資料到 Cosmos DB", False, f"錯誤: {e}")
            return False
    
    def _write_vertices(self, vertices: Dict[str, List[Dict]], incremental: bool = True) -> Dict:
        """寫入頂點資料"""
        total_written = 0
        total_updated = 0

        # 寫入使用者頂點
        for user in vertices.get('users', []):
            if self._upsert_user_vertex(user):
                total_written += 1

        # 寫入 IP 頂點
        for ip in vertices.get('ips', []):
            if self._upsert_ip_vertex(ip):
                total_written += 1

        # 寫入文章頂點
        for post in vertices.get('posts', []):
            if incremental:
                result = self._upsert_post_vertex_incremental(post)
                if result['success']:
                    total_written += 1
                    if result['updated']:
                        total_updated += 1
            else:
                if self._upsert_post_vertex(post):
                    total_written += 1

        return {
            'written': total_written,
            'updated': total_updated
        }
    
    def _write_edges(self, edges: Dict[str, List[Dict]], incremental: bool = True) -> Dict:
        """寫入邊資料"""
        total_written = 0
        new_comments = 0

        # 寫入發文關係
        for edge in edges.get('posted', []):
            if self._upsert_edge(edge, 'posted'):
                total_written += 1

        # 寫入推文關係（支援增量更新）
        for edge in edges.get('commented', []):
            if incremental:
                result = self._upsert_comment_edge_incremental(edge)
                if result['success']:
                    total_written += 1
                    if result['new']:
                        new_comments += 1
            else:
                if self._upsert_edge(edge, 'commented'):
                    total_written += 1

        # 寫入 IP 使用關係
        for edge in edges.get('used_ip', []):
            if self._upsert_edge(edge, 'used_ip'):
                total_written += 1

        # 寫入文章來源 IP 關係（支援 IP 變更追蹤）
        for edge in edges.get('from_ip', []):
            if incremental:
                result = self._upsert_from_ip_edge_incremental(edge)
                if result['success']:
                    total_written += 1
            else:
                if self._upsert_edge(edge, 'from_ip'):
                    total_written += 1

        return {
            'written': total_written,
            'new_comments': new_comments
        }
    
    def _upsert_user_vertex(self, user: Dict) -> bool:
        """插入或更新使用者頂點"""
        try:
            user_id = user['id']
            properties = user['properties']

            # 解析使用者名稱，分離 userid 和 nickname
            userid, nickname = self._parse_username(user_id)

            # 使用 upsert 語法，如果存在則更新，不存在則建立
            # 添加 partitionKey 屬性用於分割區
            query = f"""
            g.V().hasLabel('user').has('userid', '{userid}')
            .fold()
            .coalesce(
                unfold(),
                addV('user')
                .property('userid', '{userid}')
                .property('nickname', '{nickname}')
                .property('partitionKey', 'user')
            )
            .property('last_seen', '{datetime.now().isoformat()}')
            .property('first_seen', '{properties.get("first_seen", datetime.now().isoformat())}')
            """

            self.client.submit(query).all().result()
            return True

        except Exception as e:
            ptt_logger.warning(f"寫入使用者頂點失敗 {user_id}: {e}")
            return False
    
    def _upsert_ip_vertex(self, ip: Dict) -> bool:
        """插入或更新 IP 頂點"""
        try:
            ip_address = ip['id']
            properties = ip['properties']

            query = f"""
            g.V().hasLabel('ip').has('address', '{ip_address}')
            .fold()
            .coalesce(
                unfold(),
                addV('ip')
                .property('address', '{ip_address}')
                .property('partitionKey', 'ip')
            )
            .property('last_seen', '{datetime.now().isoformat()}')
            .property('first_seen', '{properties.get("first_seen", datetime.now().isoformat())}')
            """

            self.client.submit(query).all().result()
            return True

        except Exception as e:
            ptt_logger.warning(f"寫入 IP 頂點失敗 {ip_address}: {e}")
            return False
    
    def _upsert_post_vertex(self, post: Dict) -> bool:
        """插入或更新文章頂點"""
        try:
            post_id = post['id']
            properties = post['properties']

            # 清理標題中的特殊字元
            title = properties.get('title', '').replace("'", "\\'").replace('"', '\\"')

            query = f"""
            g.V().hasLabel('post').has('post_id', '{post_id}')
            .fold()
            .coalesce(
                unfold(),
                addV('post')
                .property('post_id', '{post_id}')
                .property('partitionKey', 'post')
            )
            .property('title', '{title}')
            .property('board', '{properties.get("board", "")}')
            .property('date', '{properties.get("date", "")}')
            .property('content_length', {properties.get("content_length", 0)})
            .property('updated_at', '{datetime.now().isoformat()}')
            """

            self.client.submit(query).all().result()
            return True

        except Exception as e:
            ptt_logger.warning(f"寫入文章頂點失敗 {post_id}: {e}")
            return False

    def _upsert_post_vertex_incremental(self, post: Dict) -> Dict:
        """增量更新文章頂點，檢查是否有新內容或推文"""
        try:
            post_id = post['id']
            properties = post['properties']

            # 檢查文章是否已存在
            check_query = f"""
            g.V().hasLabel('post').has('post_id', '{post_id}')
            .project('exists', 'content_length', 'updated_at')
            .by(constant(true))
            .by('content_length')
            .by('updated_at')
            """

            existing = self.client.submit(check_query).all().result()

            current_content_length = properties.get('content_length', 0)
            is_updated = False

            if existing:
                # 文章已存在，檢查是否有更新
                old_content_length = existing[0].get('content_length', 0)
                if current_content_length > old_content_length:
                    is_updated = True
                    ptt_logger.info(f"文章 {post_id} 有新內容，內容長度從 {old_content_length} 增加到 {current_content_length}")

            # 更新文章資訊
            title = properties.get('title', '').replace("'", "\\'").replace('"', '\\"')

            query = f"""
            g.V().hasLabel('post').has('post_id', '{post_id}')
            .fold()
            .coalesce(
                unfold(),
                addV('post')
                .property('post_id', '{post_id}')
                .property('partitionKey', 'post')
            )
            .property('title', '{title}')
            .property('board', '{properties.get("board", "")}')
            .property('date', '{properties.get("date", "")}')
            .property('content_length', {current_content_length})
            .property('updated_at', '{datetime.now().isoformat()}')
            """

            self.client.submit(query).all().result()

            return {
                'success': True,
                'updated': is_updated,
                'new': not bool(existing)
            }

        except Exception as e:
            ptt_logger.warning(f"增量更新文章頂點失敗 {post_id}: {e}")
            return {
                'success': False,
                'updated': False,
                'new': False
            }

    def _upsert_edge(self, edge: Dict, edge_type: str) -> bool:
        """插入或更新邊"""
        try:
            from_id = edge['from']
            to_id = edge['to']
            properties = edge.get('properties', {})
            
            # 根據邊的類型決定查詢方式
            if edge_type == 'posted':
                # 使用者發文 - 解析 from_id 取得 userid
                userid, _ = self._parse_username(from_id)
                query = f"""
                g.V().hasLabel('user').has('userid', '{userid}').as('user')
                .V().hasLabel('post').has('post_id', '{to_id}').as('post')
                .coalesce(
                    __.select('user').outE('posted').where(inV().as('post')),
                    __.select('user').addE('posted').to('post')
                        .property('date', '{properties.get("date", "")}')
                        .property('board', '{properties.get("board", "")}')
                )
                """
            
            elif edge_type == 'commented':
                # 使用者推文 - 解析 from_id 取得 userid
                userid, _ = self._parse_username(from_id)
                comment_type = properties.get('comment_type', '').replace("'", "\\'")
                content = properties.get('content', '').replace("'", "\\'").replace('"', '\\"')

                query = f"""
                g.V().hasLabel('user').has('userid', '{userid}').as('user')
                .V().hasLabel('post').has('post_id', '{to_id}').as('post')
                .select('user').addE('commented').to('post')
                .property('comment_type', '{comment_type}')
                .property('time', '{properties.get("time", "")}')
                .property('content', '{content[:100]}')
                """
            
            elif edge_type == 'used_ip':
                # 使用者使用 IP - 解析 from_id 取得 userid
                userid, _ = self._parse_username(from_id)
                query = f"""
                g.V().hasLabel('user').has('userid', '{userid}').as('user')
                .V().hasLabel('ip').has('address', '{to_id}').as('ip')
                .coalesce(
                    __.select('user').outE('used_ip').where(inV().as('ip')),
                    __.select('user').addE('used_ip').to('ip')
                        .property('date', '{properties.get("date", "")}')
                        .property('action', '{properties.get("action", "")}')
                )
                """
            
            elif edge_type == 'from_ip':
                # 文章來自 IP
                query = f"""
                g.V().hasLabel('post').has('post_id', '{from_id}').as('post')
                .V().hasLabel('ip').has('address', '{to_id}').as('ip')
                .coalesce(
                    __.select('post').outE('from_ip').where(inV().as('ip')),
                    __.select('post').addE('from_ip').to('ip')
                        .property('date', '{properties.get("date", "")}')
                )
                """
            
            else:
                ptt_logger.warning(f"未知的邊類型: {edge_type}")
                return False
            
            self.client.submit(query).all().result()
            return True
            
        except Exception as e:
            ptt_logger.warning(f"寫入邊失敗 {edge_type} {from_id}->{to_id}: {e}")
            return False

    def _upsert_comment_edge_incremental(self, edge: Dict) -> Dict:
        """增量更新推文邊，檢查是否為新推文"""
        try:
            from_id = edge['from']
            to_id = edge['to']
            properties = edge.get('properties', {})

            # 解析使用者名稱
            userid, _ = self._parse_username(from_id)
            comment_type = properties.get('comment_type', '').replace("'", "\\'")
            content = properties.get('content', '').replace("'", "\\'").replace('"', '\\"')
            comment_time = properties.get('time', '')

            # 檢查是否已存在相同的推文
            check_query = f"""
            g.V().hasLabel('user').has('userid', '{userid}')
            .outE('commented')
            .where(inV().hasLabel('post').has('post_id', '{to_id}'))
            .has('time', '{comment_time}')
            .has('content', '{content[:50]}')
            .count()
            """

            existing_count = self.client.submit(check_query).all().result()
            is_new = not (existing_count and existing_count[0] > 0)

            if is_new:
                # 新推文，記錄 IP 資訊
                user_ip = properties.get('ip', '')
                if user_ip:
                    ptt_logger.info(f"新推文來自 IP: {user_ip}")

                # 添加推文邊
                query = f"""
                g.V().hasLabel('user').has('userid', '{userid}').as('user')
                .V().hasLabel('post').has('post_id', '{to_id}').as('post')
                .select('user').addE('commented').to('post')
                .property('comment_type', '{comment_type}')
                .property('time', '{comment_time}')
                .property('content', '{content[:100]}')
                .property('ip', '{user_ip}')
                .property('created_at', '{datetime.now().isoformat()}')
                """

                self.client.submit(query).all().result()
                ptt_logger.info(f"新增推文: {userid} -> {to_id}")

            return {
                'success': True,
                'new': is_new
            }

        except Exception as e:
            ptt_logger.warning(f"增量更新推文邊失敗 {from_id}->{to_id}: {e}")
            return {
                'success': False,
                'new': False
            }

    def _upsert_from_ip_edge_incremental(self, edge: Dict) -> Dict:
        """增量更新文章來源 IP 邊，追蹤 IP 變更"""
        try:
            from_id = edge['from']  # post_id
            to_id = edge['to']      # ip_address
            properties = edge.get('properties', {})

            # 檢查文章是否已有來源 IP
            check_query = f"""
            g.V().hasLabel('post').has('post_id', '{from_id}')
            .outE('from_ip')
            .inV().hasLabel('ip')
            .values('address')
            """

            existing_ips = self.client.submit(check_query).all().result()

            if existing_ips and to_id not in existing_ips:
                # IP 發生變更，記錄變更歷史
                ptt_logger.warning(f"文章 {from_id} IP 變更: {existing_ips} -> {to_id}")

                # 添加 IP 變更記錄
                change_query = f"""
                g.V().hasLabel('post').has('post_id', '{from_id}')
                .property('ip_changed', true)
                .property('previous_ips', '{",".join(existing_ips)}')
                .property('ip_change_time', '{datetime.now().isoformat()}')
                """

                self.client.submit(change_query).all().result()

            # 添加或更新來源 IP 邊
            query = f"""
            g.V().hasLabel('post').has('post_id', '{from_id}').as('post')
            .V().hasLabel('ip').has('address', '{to_id}').as('ip')
            .coalesce(
                __.select('post').outE('from_ip').where(inV().as('ip')),
                __.select('post').addE('from_ip').to('ip')
                    .property('date', '{properties.get("date", "")}')
                    .property('created_at', '{datetime.now().isoformat()}')
            )
            """

            self.client.submit(query).all().result()

            return {
                'success': True,
                'ip_changed': bool(existing_ips and to_id not in existing_ips)
            }

        except Exception as e:
            ptt_logger.warning(f"增量更新來源 IP 邊失敗 {from_id}->{to_id}: {e}")
            return {
                'success': False,
                'ip_changed': False
            }

    def get_graph_stats(self) -> Dict:
        """取得圖形資料庫統計資訊"""
        if not self.is_connected:
            return {}
        
        try:
            stats = {}
            
            # 頂點統計
            user_count = self.client.submit("g.V().hasLabel('user').count()").all().result()
            ip_count = self.client.submit("g.V().hasLabel('ip').count()").all().result()
            post_count = self.client.submit("g.V().hasLabel('post').count()").all().result()
            
            # 邊統計
            posted_count = self.client.submit("g.E().hasLabel('posted').count()").all().result()
            commented_count = self.client.submit("g.E().hasLabel('commented').count()").all().result()
            used_ip_count = self.client.submit("g.E().hasLabel('used_ip').count()").all().result()
            from_ip_count = self.client.submit("g.E().hasLabel('from_ip').count()").all().result()
            
            stats = {
                'vertices': {
                    'users': user_count[0] if user_count else 0,
                    'ips': ip_count[0] if ip_count else 0,
                    'posts': post_count[0] if post_count else 0
                },
                'edges': {
                    'posted': posted_count[0] if posted_count else 0,
                    'commented': commented_count[0] if commented_count else 0,
                    'used_ip': used_ip_count[0] if used_ip_count else 0,
                    'from_ip': from_ip_count[0] if from_ip_count else 0
                },
                'updated_at': datetime.now().isoformat()
            }
            
            return stats
            
        except Exception as e:
            ptt_logger.error(f"取得圖形統計失敗: {e}")
            return {}
    
    def _parse_username(self, username: str) -> tuple:
        """解析使用者名稱，分離 userid 和 nickname

        Args:
            username: 完整的使用者名稱，格式如 "userid (nickname)" 或 "userid"

        Returns:
            tuple: (userid, nickname)
        """
        import re

        # 使用正則表達式匹配 "userid (nickname)" 格式
        match = re.match(r'^([^(]+)\s*\(([^)]+)\)$', username.strip())

        if match:
            userid = match.group(1).strip()
            nickname = match.group(2).strip()
        else:
            # 如果沒有括號，整個字串就是 userid，nickname 為空
            userid = username.strip()
            nickname = ''

        return userid, nickname

    def close(self):
        """關閉資料庫連接"""
        if self.client:
            try:
                self.client.close()
                self.is_connected = False
                ptt_logger.info("Cosmos DB 連接已關閉")
            except Exception as e:
                ptt_logger.warning(f"關閉 Cosmos DB 連接時發生錯誤: {e}")

    def __del__(self):
        """析構函數，確保連接被關閉"""
        self.close()
