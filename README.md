# PTT 圖形分析系統

一個基於 Python Flask 的 PTT 自動爬文與圖形關係分析系統，整合 Azure Cosmos DB 進行圖形資料儲存與查詢。

## 🚀 功能特色

### 核心功能
- **自動 PTT 爬文**：支援多看板自動爬取文章和推文
- **圖形關係分析**：分析使用者、IP、文章之間的關聯關係
- **即時可視化**：使用 Cytoscape.js 進行互動式圖形展示
- **RESTful API**：完整的 API 介面支援第三方整合
- **定時任務**：支援排程自動爬文和資料更新

### 分析功能
- **使用者關聯分析**：找出使用相同 IP 或在相同看板活動的使用者
- **IP 行為分析**：分析特定 IP 的使用者活動模式
- **文章互動網絡**：視覺化文章的推文互動關係
- **風險評估**：自動評估可疑的多重帳號行為

### 技術架構
- **後端**：Python Flask + PyPtt
- **資料庫**：Azure Cosmos DB (Gremlin API)
- **前端**：Vue 3 (Vite) + Cytoscape.js
- **部署**：Azure Web App + GitHub Actions

## 📋 系統需求

- Python 3.13+
- Azure Cosmos DB 帳戶 (Gremlin API)
- PTT 帳號
- 網路連接

## 🛠️ 安裝與設定

### 1. 克隆專案

```bash
git clone https://github.com/felaray/PyLab.Ptt.git
cd PyLab.Ptt
```

### 2. 安裝依賴套件

```bash
pip install -r requirements.txt
```

#### (可選) 前端依賴

若需開發或建置 Vue 前端，請在 `frontend/` 目錄執行：

```bash
cd frontend
npm install
```

### 3. 環境變數設定

建立 `.env` 檔案並設定以下變數：

```bash
# PTT 連接設定
PTT_USERNAME=your_ptt_username
PTT_PASSWORD=your_ptt_password
PTT_HOST=ptt.cc
PTT_PORT=23

# Azure Cosmos DB 設定
COSMOS_DB_ENDPOINT=wss://your-account.gremlin.cosmos.azure.com:443/
COSMOS_DB_KEY=your_cosmos_db_key
COSMOS_DB_DATABASE=ptt_graph_db
COSMOS_DB_COLLECTION=ptt_graph

# 系統設定
LOG_LEVEL=INFO
TIMEZONE=Asia/Taipei
DEBUG=True

# 爬蟲設定
DEFAULT_BOARDS=Test,Gossiping
MAX_POSTS_PER_BOARD=50
CRAWL_INTERVAL_HOURS=24
```

### 4. Azure Cosmos DB 設定

1. 在 Azure 入口網站建立 Cosmos DB 帳戶 (選擇 Gremlin API)
2. 建立資料庫：`ptt_graph_db`
3. 建立圖形：`ptt_graph`
4. 複製連接字串和金鑰到環境變數

### 5. 前端開發與建置

```bash
cd frontend
npm run dev    # 開發模式
npm run build  # 產生編譯檔至 ../static/dist
```

### 6. 執行應用程式

```bash
python app.py
```

應用程式將在 http://localhost:5000 啟動。

## 📖 使用說明

### 網頁介面

1. **首頁** (`/`)：系統概覽和快速導航
2. **使用者分析** (`/user-analysis`)：分析特定使用者的關聯關係
3. **IP 分析** (`/ip-analysis`)：分析 IP 位址的使用者活動
4. **文章互動** (`/post-analysis`)：查看文章的推文互動網絡
5. **系統狀態** (`/system-status`)：監控系統運行狀態
6. **爬文管理** (`/crawl-management`)：管理爬文任務

### API 介面

#### 圖形查詢 API
- `GET /api/user-links?username=<user>` - 查詢使用者關聯
- `GET /api/ip-users?ip=<ip_address>` - 查詢 IP 使用者
- `GET /api/post-interactions?post_id=<post_id>` - 查詢文章互動
- `GET /api/search-users?pattern=<pattern>` - 搜尋使用者
- `GET /api/popular-boards` - 取得熱門看板
- `GET /api/graph-stats` - 取得圖形統計

#### 系統管理 API
- `GET /api/system/status` - 系統狀態
- `GET /api/system/health` - 健康檢查
- `POST /api/system/crawl` - 觸發爬文任務
- `GET /api/system/config` - 系統配置
- `GET /api/system/logs` - 系統日誌

## 🧪 測試

### 執行測試套件

```bash
# 測試 PTT 客戶端
python test_ptt_client.py

# 測試爬蟲功能
python test_crawler.py

# 測試圖形資料庫
python test_graph_writer.py
python test_graph_query.py

# 測試 API
python test_api.py

# 測試調度器
python test_scheduler.py

# 部署前檢查
python deploy_check.py
```

## 🚀 部署

### Azure Web App 部署

1. **準備 Azure 資源**：
   - Azure Web App (Python 3.13)
   - Azure Cosmos DB (Gremlin API)

2. **設定環境變數**：
   在 Azure Web App 的「組態」→「應用程式設定」中添加所有必要的環境變數

3. **部署方式**：
   - GitHub Actions 自動部署（推薦）
   - ZIP 檔案上傳
   - Git 部署

詳細部署說明請參考 [azure-deploy.md](azure-deploy.md)

## 📊 系統架構

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Frontend  │    │   Flask API     │    │   PTT Client    │
│   (Bootstrap)   │◄──►│   (REST API)    │◄──►│   (PyPtt)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   Scheduler     │
                       │   (APScheduler) │
                       └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   Crawler       │
                       │   (Data ETL)    │
                       └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │  Azure Cosmos   │
                       │  DB (Gremlin)   │
                       └─────────────────┘
```

## 🔧 開發

### 專案結構

```
PyLab.Ptt/
├── app.py                 # Flask 主應用程式
├── config.py              # 配置管理
├── logger.py              # 日誌系統
├── ptt_client.py          # PTT 客戶端
├── crawler.py             # 爬蟲模組
├── graph_writer.py        # 圖形資料寫入
├── graph_query.py         # 圖形查詢
├── scheduler.py           # 任務調度器
├── api/                   # API 模組
│   ├── __init__.py
│   ├── graph_routes.py    # 圖形查詢 API
│   └── system_routes.py   # 系統管理 API
├── templates/             # HTML 模板
│   ├── base.html
│   ├── index.html
│   ├── user_analysis.html
│   └── ip_analysis.html
├── static/                # 靜態資源
│   └── dist/              # 前端編譯輸出 (自動產生)
├── frontend/              # Vue + Vite 前端原始碼
├── tests/                 # 測試檔案
└── requirements.txt       # 依賴套件
```

### 貢獻指南

1. Fork 專案
2. 建立功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交變更 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 開啟 Pull Request

## 📝 授權

本專案採用 MIT 授權條款 - 詳見 [LICENSE](LICENSE) 檔案

## 🤝 支援

如有問題或建議，請：

1. 查看 [Issues](https://github.com/felaray/PyLab.Ptt/issues)
2. 建立新的 Issue
3. 聯繫專案維護者

## 📈 版本歷史

- **v1.0.0** - 初始版本
  - PTT 自動登入功能
  - 基本爬文功能
  - 圖形資料庫整合
  - Web 介面和 API

## ⚠️ 注意事項

1. **合法使用**：請遵守 PTT 使用條款，適度使用爬文功能
2. **資料隱私**：妥善保護使用者資料和隱私
3. **系統負載**：避免過度頻繁的請求造成系統負擔
4. **帳號安全**：妥善保管 PTT 帳號密碼和 API 金鑰

---

**Made with ❤️ by [felaray](https://github.com/felaray)**
