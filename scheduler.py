"""
PTT 自動登入系統 - 定時任務調度器
實現每日自動觸發爬文任務的調度功能
"""

import time
import threading
from datetime import datetime, timedelta
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from crawler import PTTCrawler
from graph_writer import GraphWriter
from config import Config
from logger import ptt_logger
import json

class PTTScheduler:
    """PTT 定時任務調度器"""
    
    def __init__(self):
        self.scheduler = BackgroundScheduler()
        self.is_running = False
        self.last_crawl_time = None
        self.crawl_results = []
        self.max_results_history = 10  # 保留最近10次結果
        
    def start(self):
        """啟動調度器"""
        try:
            if not self.is_running:
                self.scheduler.start()
                self.is_running = True
                ptt_logger.info("PTT 定時任務調度器已啟動")
                
                # 添加預設的每日爬文任務
                self.schedule_daily_crawl()
                
                return True
            else:
                ptt_logger.warning("調度器已經在運行中")
                return False
                
        except Exception as e:
            ptt_logger.error(f"啟動調度器失敗: {e}")
            return False
    
    def stop(self):
        """停止調度器"""
        try:
            if self.is_running:
                self.scheduler.shutdown()
                self.is_running = False
                ptt_logger.info("PTT 定時任務調度器已停止")
                return True
            else:
                ptt_logger.warning("調度器未在運行")
                return False
                
        except Exception as e:
            ptt_logger.error(f"停止調度器失敗: {e}")
            return False
    
    def schedule_daily_crawl(self, hour: int = 8, minute: int = 0):
        """排程每日爬文任務
        
        Args:
            hour: 執行小時，預設8點
            minute: 執行分鐘，預設0分
        """
        try:
            # 移除現有的每日爬文任務
            try:
                self.scheduler.remove_job('daily_crawl')
            except:
                pass
            
            # 添加新的每日爬文任務
            self.scheduler.add_job(
                func=self.execute_daily_crawl,
                trigger=CronTrigger(hour=hour, minute=minute),
                id='daily_crawl',
                name='每日 PTT 爬文任務',
                replace_existing=True
            )
            
            ptt_logger.info(f"已排程每日爬文任務: 每天 {hour:02d}:{minute:02d}")
            return True
            
        except Exception as e:
            ptt_logger.error(f"排程每日爬文任務失敗: {e}")
            return False
    
    def schedule_interval_crawl(self, hours: int = None):
        """排程間隔爬文任務
        
        Args:
            hours: 間隔小時數，預設使用配置中的值
        """
        try:
            hours = hours or Config.CRAWL_INTERVAL_HOURS
            
            # 移除現有的間隔爬文任務
            try:
                self.scheduler.remove_job('interval_crawl')
            except:
                pass
            
            # 添加新的間隔爬文任務
            self.scheduler.add_job(
                func=self.execute_daily_crawl,
                trigger=IntervalTrigger(hours=hours),
                id='interval_crawl',
                name=f'間隔 PTT 爬文任務 ({hours}小時)',
                replace_existing=True
            )
            
            ptt_logger.info(f"已排程間隔爬文任務: 每 {hours} 小時執行一次")
            return True
            
        except Exception as e:
            ptt_logger.error(f"排程間隔爬文任務失敗: {e}")
            return False
    
    def execute_daily_crawl(self):
        """執行每日爬文任務"""
        try:
            ptt_logger.log_operation_start("執行定時爬文任務")
            
            # 檢查 PTT 配置
            config_errors = Config.validate_config()
            if config_errors:
                ptt_logger.error(f"PTT 配置不完整，跳過爬文任務: {config_errors}")
                return False
            
            # 建立爬蟲實例
            crawler = PTTCrawler()
            
            # 執行爬文
            boards = Config.DEFAULT_BOARDS
            max_posts = Config.MAX_POSTS_PER_BOARD
            
            ptt_logger.info(f"開始爬取看板: {boards}, 每板最大文章數: {max_posts}")
            
            crawl_result = crawler.crawl_boards(boards, max_posts)
            
            if crawl_result['success']:
                # 記錄爬文結果
                self.last_crawl_time = datetime.now()
                
                # 嘗試寫入圖形資料庫
                graph_write_success = False
                try:
                    writer = GraphWriter()
                    if writer.is_connected:
                        graph_data = crawler.generate_graph_data(crawl_result)
                        graph_write_success = writer.write_graph_data(graph_data)
                        ptt_logger.info(f"圖形資料寫入: {'成功' if graph_write_success else '失敗'}")
                    else:
                        ptt_logger.warning("圖形資料庫未連接，跳過資料寫入")
                except Exception as e:
                    ptt_logger.error(f"圖形資料寫入錯誤: {e}")
                
                # 儲存結果到歷史記錄
                result_summary = {
                    'timestamp': self.last_crawl_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'success': True,
                    'boards_count': crawl_result['summary']['boards_count'],
                    'total_posts': crawl_result['summary']['total_posts'],
                    'total_users': crawl_result['summary']['total_users'],
                    'total_ips': crawl_result['summary']['total_ips'],
                    'graph_write_success': graph_write_success
                }
                
                self.crawl_results.append(result_summary)
                
                # 保持歷史記錄數量限制
                if len(self.crawl_results) > self.max_results_history:
                    self.crawl_results = self.crawl_results[-self.max_results_history:]
                
                # 儲存詳細結果到檔案
                timestamp = self.last_crawl_time.strftime('%Y%m%d_%H%M%S')
                filename = f"scheduled_crawl_result_{timestamp}.json"
                crawler.save_crawl_result(crawl_result, filename)
                
                ptt_logger.log_operation_end("執行定時爬文任務", True, 
                                           f"成功爬取 {result_summary['boards_count']} 個看板, "
                                           f"{result_summary['total_posts']} 篇文章")
                return True
                
            else:
                ptt_logger.log_operation_end("執行定時爬文任務", False, "爬文失敗")
                
                # 記錄失敗結果
                result_summary = {
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'success': False,
                    'error': '爬文任務執行失敗'
                }
                self.crawl_results.append(result_summary)
                
                return False
                
        except Exception as e:
            ptt_logger.log_operation_end("執行定時爬文任務", False, f"錯誤: {e}")
            
            # 記錄異常結果
            result_summary = {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'success': False,
                'error': str(e)
            }
            self.crawl_results.append(result_summary)
            
            return False
    
    def execute_manual_crawl(self, boards=None, max_posts=None):
        """執行手動爬文任務
        
        Args:
            boards: 看板列表，預設使用配置
            max_posts: 每板最大文章數，預設使用配置
            
        Returns:
            dict: 爬文結果
        """
        try:
            ptt_logger.log_operation_start("執行手動爬文任務")
            
            boards = boards or Config.DEFAULT_BOARDS
            max_posts = max_posts or Config.MAX_POSTS_PER_BOARD
            
            # 建立爬蟲實例
            crawler = PTTCrawler()
            
            # 執行爬文
            crawl_result = crawler.crawl_boards(boards, max_posts)
            
            if crawl_result['success']:
                ptt_logger.log_operation_end("執行手動爬文任務", True, 
                                           f"成功爬取 {crawl_result['summary']['boards_count']} 個看板")
            else:
                ptt_logger.log_operation_end("執行手動爬文任務", False, "爬文失敗")
            
            return crawl_result
            
        except Exception as e:
            ptt_logger.log_operation_end("執行手動爬文任務", False, f"錯誤: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
    
    def get_status(self):
        """取得調度器狀態"""
        try:
            jobs = []
            if self.is_running:
                for job in self.scheduler.get_jobs():
                    jobs.append({
                        'id': job.id,
                        'name': job.name,
                        'next_run': job.next_run_time.strftime('%Y-%m-%d %H:%M:%S') if job.next_run_time else None,
                        'trigger': str(job.trigger)
                    })
            
            return {
                'is_running': self.is_running,
                'last_crawl_time': self.last_crawl_time.strftime('%Y-%m-%d %H:%M:%S') if self.last_crawl_time else None,
                'jobs': jobs,
                'recent_results': self.crawl_results[-5:],  # 最近5次結果
                'total_results': len(self.crawl_results)
            }
            
        except Exception as e:
            ptt_logger.error(f"取得調度器狀態失敗: {e}")
            return {
                'is_running': self.is_running,
                'error': str(e)
            }
    
    def get_next_crawl_time(self):
        """取得下次爬文時間"""
        try:
            if not self.is_running:
                return None
            
            jobs = self.scheduler.get_jobs()
            if jobs:
                next_times = [job.next_run_time for job in jobs if job.next_run_time]
                if next_times:
                    return min(next_times)
            
            return None
            
        except Exception as e:
            ptt_logger.error(f"取得下次爬文時間失敗: {e}")
            return None

# 建立全域調度器實例
ptt_scheduler = PTTScheduler()
