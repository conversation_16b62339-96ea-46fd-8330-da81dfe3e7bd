<template>
  <div class="graph-query-page">
    <!-- 頁面標題 -->
    <div class="page-header mb-4">
      <div class="row align-items-center">
        <div class="col">
          <h2 class="mb-0">
            <i class="bi bi-diagram-3 text-primary"></i>
            圖形化社交網絡分析
          </h2>
          <p class="text-muted mb-0">探索PTT使用者之間的複雜關聯關係</p>
        </div>
        <div class="col-auto">
          <div class="btn-group" role="group">
            <button 
              class="btn btn-outline-primary"
              @click="showTutorial = true"
            >
              <i class="bi bi-question-circle"></i> 使用教學
            </button>
            <button 
              class="btn btn-outline-success"
              @click="loadSampleData"
            >
              <i class="bi bi-play-circle"></i> 載入範例
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <!-- 左側查詢面板 -->
      <div class="col-lg-4">
        <div class="query-panel">
          <!-- 查詢類型選擇 -->
          <div class="card mb-3">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-search"></i> 查詢設定
              </h5>
            </div>
            <div class="card-body">
              <!-- 查詢類型 -->
              <div class="mb-3">
                <label class="form-label">查詢類型</label>
                <select class="form-select" v-model="queryType" @change="resetQuery">
                  <option value="user-network-simple">使用者社交網絡 (簡化版)</option>
                  <option value="user-network">使用者社交網絡 (完整版)</option>
                  <option value="ip-analysis">IP關聯分析</option>
                  <option value="post-interaction">文章互動網絡</option>
                  <option value="multi-layer">多層關係探索</option>
                </select>
                <div class="form-text">
                  {{ getQueryTypeDescription(queryType) }}
                </div>
              </div>

              <!-- 動態查詢參數 -->
              <div v-if="queryType === 'user-network-simple' || queryType === 'user-network'">
                <div class="mb-3">
                  <label class="form-label">使用者名稱</label>
                  <input
                    type="text"
                    class="form-control"
                    v-model="queryParams.username"
                    placeholder="輸入使用者名稱或暱稱"
                    @keyup.enter="executeQuery"
                  >
                </div>
                <div class="mb-3">
                  <label class="form-label">關聯深度: {{ queryParams.depth }}</label>
                  <input
                    type="range"
                    class="form-range"
                    min="1"
                    max="4"
                    v-model="queryParams.depth"
                  >
                  <div class="form-text">
                    深度越高，顯示的關聯層級越多
                    <span v-if="queryType === 'user-network-simple'" class="text-success">
                      (簡化版：更快速、更穩定)
                    </span>
                  </div>
                </div>
              </div>

              <div v-else-if="queryType === 'ip-analysis'">
                <div class="mb-3">
                  <label class="form-label">IP位址</label>
                  <input 
                    type="text" 
                    class="form-control" 
                    v-model="queryParams.ip"
                    placeholder="例: ***********"
                    @keyup.enter="executeQuery"
                  >
                </div>
              </div>

              <div v-else-if="queryType === 'post-interaction'">
                <div class="mb-3">
                  <label class="form-label">文章ID</label>
                  <input 
                    type="text" 
                    class="form-control" 
                    v-model="queryParams.postId"
                    placeholder="例: Test.M.123456"
                    @keyup.enter="executeQuery"
                  >
                </div>
              </div>

              <div v-else-if="queryType === 'multi-layer'">
                <div class="mb-3">
                  <label class="form-label">起始使用者</label>
                  <input 
                    type="text" 
                    class="form-control" 
                    v-model="queryParams.username"
                    placeholder="輸入使用者名稱"
                    @keyup.enter="executeQuery"
                  >
                </div>
                <div class="mb-3">
                  <label class="form-label">關聯類型</label>
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" v-model="queryParams.includeIp" id="includeIp">
                    <label class="form-check-label" for="includeIp">
                      包含IP關聯
                    </label>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" v-model="queryParams.includePosts" id="includePosts">
                    <label class="form-check-label" for="includePosts">
                      包含文章關聯
                    </label>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" v-model="queryParams.includeComments" id="includeComments">
                    <label class="form-check-label" for="includeComments">
                      包含推文關聯
                    </label>
                  </div>
                </div>
              </div>

              <!-- 執行查詢按鈕 -->
              <button 
                class="btn btn-primary w-100"
                @click="executeQuery"
                :disabled="isQuerying || !canExecuteQuery"
              >
                <span v-if="isQuerying" class="spinner-border spinner-border-sm me-2"></span>
                <i v-else class="bi bi-play-fill"></i>
                {{ isQuerying ? '查詢中...' : '執行查詢' }}
              </button>
            </div>
          </div>

          <!-- 查詢歷史 -->
          <div class="card mb-3" v-if="queryHistory.length > 0">
            <div class="card-header">
              <h6 class="mb-0">
                <i class="bi bi-clock-history"></i> 查詢歷史
              </h6>
            </div>
            <div class="card-body">
              <div class="list-group list-group-flush">
                <button 
                  v-for="(history, index) in queryHistory.slice(0, 5)" 
                  :key="index"
                  class="list-group-item list-group-item-action p-2"
                  @click="loadFromHistory(history)"
                >
                  <div class="d-flex justify-content-between align-items-center">
                    <div>
                      <small class="text-muted">{{ history.type }}</small><br>
                      <span class="fw-bold">{{ history.params }}</span>
                    </div>
                    <small class="text-muted">{{ formatTime(history.timestamp) }}</small>
                  </div>
                </button>
              </div>
            </div>
          </div>

          <!-- 圖形分析洞察 -->
          <div class="card" v-if="graphInsights.length > 0">
            <div class="card-header">
              <h6 class="mb-0">
                <i class="bi bi-lightbulb"></i> 分析洞察
              </h6>
            </div>
            <div class="card-body">
              <div v-for="(insight, index) in graphInsights" :key="index" class="mb-2">
                <div class="alert alert-info alert-sm mb-2">
                  <i :class="insight.icon"></i>
                  {{ insight.message }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右側圖形顯示區域 -->
      <div class="col-lg-8">
        <div class="graph-display">
          <!-- 查詢結果標題 -->
          <div v-if="queryResult" class="result-header mb-3">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h5 class="mb-1">
                  <i class="bi bi-diagram-2"></i>
                  {{ getResultTitle() }}
                </h5>
                <p class="text-muted mb-0">
                  {{ getResultDescription() }}
                </p>
              </div>
              <div class="btn-group" role="group">
                <button 
                  class="btn btn-outline-primary btn-sm"
                  @click="shareGraph"
                  :disabled="!queryResult"
                >
                  <i class="bi bi-share"></i> 分享
                </button>
                <button 
                  class="btn btn-outline-success btn-sm"
                  @click="exportData"
                  :disabled="!queryResult"
                >
                  <i class="bi bi-download"></i> 匯出
                </button>
              </div>
            </div>
          </div>

          <!-- 網絡圖組件 -->
          <NetworkGraph
            ref="networkGraph"
            :graph-data="graphData"
            :height="600"
            :auto-layout="true"
            @node-click="onNodeClick"
            @edge-click="onEdgeClick"
            @expand-node="onExpandNode"
          />

          <!-- 空狀態 -->
          <div v-if="!queryResult && !isQuerying" class="empty-state text-center py-5">
            <i class="bi bi-diagram-3 text-muted" style="font-size: 4rem;"></i>
            <h4 class="text-muted mt-3">開始探索社交網絡</h4>
            <p class="text-muted">
              選擇查詢類型並輸入參數，開始分析PTT使用者之間的關聯關係
            </p>
            <button class="btn btn-primary" @click="loadSampleData">
              <i class="bi bi-play-circle"></i> 載入範例資料
            </button>
          </div>

          <!-- 錯誤狀態 -->
          <div v-if="queryError" class="alert alert-danger mt-3">
            <i class="bi bi-exclamation-triangle"></i>
            <strong>查詢失敗:</strong> {{ queryError }}
            <button class="btn btn-outline-danger btn-sm ms-2" @click="queryError = null">
              <i class="bi bi-x"></i> 關閉
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 使用教學模態框 -->
    <div class="modal fade" :class="{ show: showTutorial }" :style="{ display: showTutorial ? 'block' : 'none' }" tabindex="-1">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="bi bi-question-circle"></i> 圖形化社交網絡分析使用教學
            </h5>
            <button type="button" class="btn-close" @click="showTutorial = false"></button>
          </div>
          <div class="modal-body">
            <div class="row">
              <div class="col-md-6">
                <h6><i class="bi bi-1-circle text-primary"></i> 使用者社交網絡</h6>
                <p>分析特定使用者與其他使用者的互動關係，包括：</p>
                <ul>
                  <li>共同發文的看板</li>
                  <li>互相推文的關係</li>
                  <li>使用相同IP的關聯</li>
                </ul>
              </div>
              <div class="col-md-6">
                <h6><i class="bi bi-2-circle text-success"></i> IP關聯分析</h6>
                <p>探索特定IP位址的使用模式：</p>
                <ul>
                  <li>使用該IP的所有帳號</li>
                  <li>時間分布分析</li>
                  <li>可能的帳號關聯</li>
                </ul>
              </div>
            </div>
            <div class="row mt-3">
              <div class="col-md-6">
                <h6><i class="bi bi-3-circle text-warning"></i> 文章互動網絡</h6>
                <p>視覺化文章的推文互動：</p>
                <ul>
                  <li>推文者之間的關係</li>
                  <li>推文類型分布</li>
                  <li>互動強度分析</li>
                </ul>
              </div>
              <div class="col-md-6">
                <h6><i class="bi bi-4-circle text-info"></i> 多層關係探索</h6>
                <p>綜合分析多種關聯類型：</p>
                <ul>
                  <li>使用者-IP-文章三層關係</li>
                  <li>複雜網絡結構</li>
                  <li>深度關聯挖掘</li>
                </ul>
              </div>
            </div>
            <div class="mt-4">
              <h6><i class="bi bi-mouse text-primary"></i> 互動操作</h6>
              <ul>
                <li><strong>點擊節點</strong>：查看詳細資訊</li>
                <li><strong>拖拽節點</strong>：調整圖形佈局</li>
                <li><strong>滾輪縮放</strong>：放大縮小視圖</li>
                <li><strong>展開關聯</strong>：探索更深層的連接</li>
              </ul>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-primary" @click="showTutorial = false">
              開始使用
            </button>
          </div>
        </div>
      </div>
    </div>
    <div v-if="showTutorial" class="modal-backdrop fade show"></div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import NetworkGraph from './NetworkGraph.vue'

// 響應式資料
const showTutorial = ref(false)
const queryType = ref('user-network-simple')
const queryParams = ref({
  username: '',
  ip: '',
  postId: '',
  depth: 2,
  includeIp: true,
  includePosts: true,
  includeComments: true
})

const isQuerying = ref(false)
const queryResult = ref(null)
const queryError = ref(null)
const graphData = ref({ nodes: [], edges: [] })
const queryHistory = ref([])
const graphInsights = ref([])

// 組件引用
const networkGraph = ref(null)

// 計算屬性
const canExecuteQuery = computed(() => {
  switch (queryType.value) {
    case 'user-network-simple':
    case 'user-network':
    case 'multi-layer':
      return queryParams.value.username.trim().length > 0
    case 'ip-analysis':
      return queryParams.value.ip.trim().length > 0
    case 'post-interaction':
      return queryParams.value.postId.trim().length > 0
    default:
      return false
  }
})

// 方法
function getQueryTypeDescription(type) {
  const descriptions = {
    'user-network-simple': '分析使用者與其他使用者的互動關係和社交網絡 (簡化版，更快速穩定)',
    'user-network': '分析使用者與其他使用者的互動關係和社交網絡 (完整版)',
    'ip-analysis': '探索特定IP位址的使用者活動和關聯模式',
    'post-interaction': '視覺化文章的推文互動網絡和參與者關係',
    'multi-layer': '綜合分析使用者、IP、文章之間的多層關聯關係'
  }
  return descriptions[type] || ''
}

function resetQuery() {
  queryParams.value = {
    username: '',
    ip: '',
    postId: '',
    depth: 2,
    includeIp: true,
    includePosts: true,
    includeComments: true
  }
  queryResult.value = null
  queryError.value = null
  graphData.value = { nodes: [], edges: [] }
  graphInsights.value = []
}

async function executeQuery() {
  if (!canExecuteQuery.value) return

  isQuerying.value = true
  queryError.value = null
  graphInsights.value = []

  try {
    let apiUrl = ''
    let params = new URLSearchParams()

    // 根據查詢類型選擇API
    if (queryType.value === 'user-network-simple') {
      // 使用簡化版API
      apiUrl = '/api/user-network-simple'
      params.append('username', queryParams.value.username)
      params.append('max_depth', queryParams.value.depth)
    } else {
      // 使用統一的圖形網絡API
      apiUrl = '/api/graph-network'
      params.append('type', queryType.value)

      // 根據查詢類型添加參數
      switch (queryType.value) {
        case 'user-network':
          params.append('username', queryParams.value.username)
          params.append('depth', queryParams.value.depth)
          break

        case 'ip-analysis':
          params.append('ip', queryParams.value.ip)
          break

        case 'post-interaction':
          params.append('post_id', queryParams.value.postId)
          break

        case 'multi-layer':
          params.append('username', queryParams.value.username)
          params.append('depth', queryParams.value.depth)
          params.append('include_ip', queryParams.value.includeIp)
          params.append('include_posts', queryParams.value.includePosts)
          params.append('include_comments', queryParams.value.includeComments)
          break
      }
    }

    const response = await fetch(`${apiUrl}?${params}`)
    const data = await response.json()

    if (data.success) {
      queryResult.value = data
      // 處理不同API的資料格式
      if (queryType.value === 'user-network-simple') {
        // 簡化版API直接返回網絡資料
        graphData.value = data.data
      } else {
        // 其他API返回圖形資料格式
        graphData.value = data.data
      }
      addToHistory()
      generateInsights(data.data)
    } else {
      queryError.value = data.error || '查詢失敗'
    }

  } catch (error) {
    queryError.value = `網絡錯誤: ${error.message}`
  } finally {
    isQuerying.value = false
  }
}

function processQueryResult(data) {
  // 將API返回的資料轉換為圖形資料格式
  const nodes = []
  const edges = []

  if (queryType.value === 'user-network' || queryType.value === 'multi-layer') {
    // 處理使用者網絡資料
    if (data.user) {
      nodes.push({
        id: data.userid || data.user,
        label: data.user,
        type: 'user',
        metadata: {
          暱稱: data.nickname || '',
          文章數: data.posts?.length || 0,
          推文數: data.comments?.length || 0,
          IP數: data.ips?.length || 0
        }
      })
    }

    // 添加關聯使用者
    if (data.connections) {
      data.connections.forEach(conn => {
        const targetId = conn.target_user
        if (!nodes.find(n => n.id === targetId)) {
          nodes.push({
            id: targetId,
            label: targetId,
            type: 'user',
            metadata: {
              關聯類型: conn.type,
              共同項目: conn.shared_items?.length || 0
            }
          })
        }

        edges.push({
          source: data.userid || data.user,
          target: targetId,
          label: getConnectionTypeLabel(conn.type),
          type: conn.type,
          weight: conn.shared_items?.length || 1
        })
      })
    }

    // 添加IP節點
    if (data.ips) {
      data.ips.forEach(ip => {
        const ipId = `ip_${ip}`
        nodes.push({
          id: ipId,
          label: ip,
          type: 'ip',
          metadata: {
            IP位址: ip
          }
        })

        edges.push({
          source: data.userid || data.user,
          target: ipId,
          label: '使用IP',
          type: 'used_ip'
        })
      })
    }

  } else if (queryType.value === 'ip-analysis') {
    // 處理IP分析資料
    if (data.ip) {
      nodes.push({
        id: `ip_${data.ip}`,
        label: data.ip,
        type: 'ip',
        metadata: {
          IP位址: data.ip,
          使用者數: data.users?.length || 0
        }
      })
    }

    // 添加使用該IP的使用者
    if (data.users) {
      data.users.forEach(user => {
        const userId = user.userid || user.username
        nodes.push({
          id: userId,
          label: userId,
          type: 'user',
          metadata: {
            暱稱: user.nickname || '',
            首次使用: user.first_seen || '',
            最後使用: user.last_seen || ''
          }
        })

        edges.push({
          source: `ip_${data.ip}`,
          target: userId,
          label: '使用者',
          type: 'used_by'
        })
      })
    }

  } else if (queryType.value === 'post-interaction') {
    // 處理文章互動資料
    if (data.post) {
      nodes.push({
        id: data.post.post_id,
        label: data.post.title || data.post.post_id,
        type: 'post',
        metadata: {
          標題: data.post.title || '',
          看板: data.post.board || '',
          日期: data.post.date || '',
          作者: data.author || ''
        }
      })
    }

    // 添加推文者
    if (data.commenters) {
      data.commenters.forEach(commenter => {
        const commenterId = commenter.userid || commenter.username
        if (!nodes.find(n => n.id === commenterId)) {
          nodes.push({
            id: commenterId,
            label: commenterId,
            type: 'user',
            metadata: {
              推文數: commenter.comment_count || 0,
              推文類型: commenter.comment_types?.join(', ') || ''
            }
          })
        }

        edges.push({
          source: commenterId,
          target: data.post.post_id,
          label: '推文',
          type: 'commented',
          weight: commenter.comment_count || 1
        })
      })
    }
  }

  graphData.value = { nodes, edges }
}

function getConnectionTypeLabel(type) {
  const labels = {
    'posted_same_board': '同板發文',
    'commented_same_post': '同文推文',
    'used_same_ip': '共用IP'
  }
  return labels[type] || type
}

function addToHistory() {
  const historyItem = {
    type: getQueryTypeDescription(queryType.value),
    params: getQueryParamsString(),
    timestamp: Date.now(),
    queryType: queryType.value,
    queryParams: { ...queryParams.value }
  }

  queryHistory.value.unshift(historyItem)
  if (queryHistory.value.length > 10) {
    queryHistory.value = queryHistory.value.slice(0, 10)
  }

  // 保存到localStorage
  localStorage.setItem('ptt_query_history', JSON.stringify(queryHistory.value))
}

function getQueryParamsString() {
  switch (queryType.value) {
    case 'user-network':
    case 'multi-layer':
      return queryParams.value.username
    case 'ip-analysis':
      return queryParams.value.ip
    case 'post-interaction':
      return queryParams.value.postId
    default:
      return ''
  }
}

function loadFromHistory(history) {
  queryType.value = history.queryType
  queryParams.value = { ...history.queryParams }
  executeQuery()
}

function formatTime(timestamp) {
  const date = new Date(timestamp)
  return date.toLocaleString('zh-TW', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

function generateInsights(data) {
  const insights = []

  const nodeCount = data.nodes?.length || 0
  const edgeCount = data.edges?.length || 0
  const userNodes = data.nodes?.filter(n => n.type === 'user') || []
  const ipNodes = data.nodes?.filter(n => n.type === 'ip') || []

  if (queryType.value === 'user-network') {
    const connectionCount = edgeCount
    const ipCount = ipNodes.length

    if (connectionCount > 5) {
      insights.push({
        icon: 'bi bi-people-fill',
        message: `該使用者有 ${connectionCount} 個關聯，社交活躍度較高`
      })
    }

    if (ipCount > 3) {
      insights.push({
        icon: 'bi bi-geo-alt-fill',
        message: `使用了 ${ipCount} 個不同IP，可能有多個上網地點`
      })
    }

    if (connectionCount === 0) {
      insights.push({
        icon: 'bi bi-info-circle',
        message: '該使用者較少與其他使用者互動，可能是新用戶或潛水用戶'
      })
    }

    // 分析關係類型
    const relationshipTypes = {}
    data.edges?.forEach(edge => {
      const type = edge.type || 'unknown'
      relationshipTypes[type] = (relationshipTypes[type] || 0) + 1
    })

    if (relationshipTypes.commented_on && relationshipTypes.commented_by) {
      insights.push({
        icon: 'bi bi-chat-dots',
        message: '該使用者既推別人的文，也被別人推文，互動性良好'
      })
    }

    if (relationshipTypes.multiple) {
      insights.push({
        icon: 'bi bi-diagram-3',
        message: '發現多重關係連接，表示使用者間有深度互動'
      })
    }
  }

  if (queryType.value === 'ip-analysis') {
    const userCount = userNodes.length

    if (userCount > 1) {
      insights.push({
        icon: 'bi bi-exclamation-triangle',
        message: `該IP被 ${userCount} 個使用者使用，可能是共用IP或代理伺服器`
      })
    }

    if (userCount === 1) {
      insights.push({
        icon: 'bi bi-check-circle',
        message: '該IP僅被一個使用者使用，IP使用模式正常'
      })
    }
  }

  if (queryType.value === 'post-interaction') {
    const commenterCount = userNodes.length

    if (commenterCount > 10) {
      insights.push({
        icon: 'bi bi-fire',
        message: `該文章有 ${commenterCount} 個推文者，是熱門文章`
      })
    }

    if (commenterCount === 0) {
      insights.push({
        icon: 'bi bi-chat-dots',
        message: '該文章沒有推文，可能是冷門或新發布的文章'
      })
    }
  }

  if (queryType.value === 'multi-layer') {
    insights.push({
      icon: 'bi bi-layers',
      message: `多層關係分析顯示 ${nodeCount} 個節點和 ${edgeCount} 個關係`
    })

    if (nodeCount > 20) {
      insights.push({
        icon: 'bi bi-diagram-3',
        message: '複雜的關係網絡，建議使用篩選功能聚焦特定關係'
      })
    }
  }

  graphInsights.value = insights
}

function getResultTitle() {
  const titles = {
    'user-network': `使用者 "${queryParams.value.username}" 的社交網絡`,
    'ip-analysis': `IP "${queryParams.value.ip}" 的使用者分析`,
    'post-interaction': `文章 "${queryParams.value.postId}" 的互動網絡`,
    'multi-layer': `使用者 "${queryParams.value.username}" 的多層關係分析`
  }
  return titles[queryType.value] || '查詢結果'
}

function getResultDescription() {
  if (!queryResult.value) return ''

  const data = queryResult.value.data
  const nodeCount = graphData.value.nodes.length
  const edgeCount = graphData.value.edges.length

  return `找到 ${nodeCount} 個節點和 ${edgeCount} 個關係連接`
}

// 事件處理
function onNodeClick(nodeData) {
  console.log('節點點擊:', nodeData)
  // 可以在這裡添加節點點擊的處理邏輯
}

function onEdgeClick(edgeData) {
  console.log('邊點擊:', edgeData)
  // 可以在這裡添加邊點擊的處理邏輯
}

function onExpandNode(nodeData) {
  console.log('展開節點:', nodeData)
  // 可以在這裡添加展開節點的邏輯，例如查詢更多關聯資料
}

function shareGraph() {
  // 生成分享連結
  const shareData = {
    queryType: queryType.value,
    queryParams: queryParams.value
  }

  const shareUrl = `${window.location.origin}${window.location.pathname}?share=${encodeURIComponent(JSON.stringify(shareData))}`

  if (navigator.share) {
    navigator.share({
      title: 'PTT 社交網絡分析',
      text: getResultTitle(),
      url: shareUrl
    })
  } else {
    // 複製到剪貼板
    navigator.clipboard.writeText(shareUrl).then(() => {
      alert('分享連結已複製到剪貼板')
    })
  }
}

function exportData() {
  const exportData = {
    queryInfo: {
      type: queryType.value,
      params: queryParams.value,
      timestamp: new Date().toISOString()
    },
    graphData: graphData.value,
    insights: graphInsights.value
  }

  const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `ptt-network-analysis-${Date.now()}.json`
  link.click()
  URL.revokeObjectURL(url)
}

function loadSampleData() {
  // 載入範例資料
  queryType.value = 'user-network'
  queryParams.value.username = 'felaray'
  queryParams.value.depth = 2
  executeQuery()
}

// 生命週期
onMounted(() => {
  // 載入查詢歷史
  const savedHistory = localStorage.getItem('ptt_query_history')
  if (savedHistory) {
    try {
      queryHistory.value = JSON.parse(savedHistory)
    } catch (e) {
      console.error('載入查詢歷史失敗:', e)
    }
  }

  // 檢查是否有分享參數
  const urlParams = new URLSearchParams(window.location.search)
  const shareParam = urlParams.get('share')
  if (shareParam) {
    try {
      const shareData = JSON.parse(decodeURIComponent(shareParam))
      queryType.value = shareData.queryType
      queryParams.value = { ...queryParams.value, ...shareData.queryParams }
      executeQuery()
    } catch (e) {
      console.error('載入分享資料失敗:', e)
    }
  }
})
</script>

<style scoped>
.graph-query-page {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  border-radius: 15px;
  margin-bottom: 30px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.page-header h2 {
  font-weight: 700;
  margin-bottom: 8px;
}

.page-header p {
  opacity: 0.9;
  font-size: 1.1em;
}

.query-panel .card {
  border: none;
  box-shadow: 0 5px 15px rgba(0,0,0,0.08);
  border-radius: 12px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.query-panel .card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.12);
}

.query-panel .card-header {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  border-radius: 12px 12px 0 0 !important;
  border: none;
  padding: 15px 20px;
}

.query-panel .card-header h5,
.query-panel .card-header h6 {
  margin: 0;
  font-weight: 600;
}

.form-control, .form-select {
  border-radius: 8px;
  border: 2px solid #e9ecef;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-control:focus, .form-select:focus {
  border-color: #4facfe;
  box-shadow: 0 0 0 0.2rem rgba(79, 172, 254, 0.25);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 600;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-outline-primary, .btn-outline-success, .btn-outline-danger {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.list-group-item-action {
  border-radius: 8px !important;
  margin-bottom: 5px;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
}

.list-group-item-action:hover {
  background-color: #f8f9fa;
  transform: translateX(5px);
}

.graph-display {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  min-height: 700px;
}

.result-header {
  padding: 20px;
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  border-radius: 12px;
  margin-bottom: 20px;
}

.result-header h5 {
  color: #2c3e50;
  font-weight: 700;
  margin-bottom: 5px;
}

.result-header p {
  color: #5a6c7d;
  margin: 0;
}

.empty-state {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  border-radius: 15px;
  padding: 60px 40px;
  text-align: center;
}

.empty-state i {
  opacity: 0.7;
  margin-bottom: 20px;
}

.empty-state h4 {
  color: #8b5a3c;
  font-weight: 600;
  margin-bottom: 15px;
}

.empty-state p {
  color: #a0725a;
  font-size: 1.1em;
  margin-bottom: 25px;
}

.alert-info.alert-sm {
  padding: 8px 12px;
  font-size: 0.9em;
  border-radius: 8px;
  border: none;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  color: #1565c0;
}

.modal-content {
  border-radius: 15px;
  border: none;
  box-shadow: 0 20px 60px rgba(0,0,0,0.2);
}

.modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 15px 15px 0 0;
  border: none;
  padding: 20px 25px;
}

.modal-title {
  font-weight: 600;
  margin: 0;
}

.modal-body {
  padding: 25px;
}

.modal-body h6 {
  font-weight: 600;
  margin-bottom: 10px;
}

.modal-body ul {
  margin-bottom: 15px;
}

.modal-body li {
  margin-bottom: 5px;
  color: #5a6c7d;
}

.btn-close {
  filter: brightness(0) invert(1);
}

/* 響應式設計 */
@media (max-width: 768px) {
  .graph-query-page {
    padding: 10px;
  }

  .page-header {
    padding: 20px;
    text-align: center;
  }

  .page-header .btn-group {
    margin-top: 15px;
  }

  .graph-display {
    padding: 15px;
  }

  .result-header {
    padding: 15px;
  }

  .empty-state {
    padding: 40px 20px;
  }
}

/* 動畫效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.query-panel .card,
.graph-display {
  animation: fadeInUp 0.6s ease-out;
}

.query-panel .card:nth-child(2) {
  animation-delay: 0.1s;
}

.query-panel .card:nth-child(3) {
  animation-delay: 0.2s;
}

/* 載入動畫 */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.spinner-border {
  animation: pulse 1s infinite;
}

/* 自定義滾動條 */
.card-body::-webkit-scrollbar {
  width: 6px;
}

.card-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.card-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.card-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
