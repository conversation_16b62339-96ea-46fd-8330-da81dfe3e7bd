# PTT 圖形分析系統 API 文件

## 基本資訊

- **基礎 URL**: `http://localhost:5000/api`
- **回應格式**: JSON
- **編碼**: UTF-8

## API 端點

### 1. 列出所有使用者

**端點**: `GET /api/users`

**描述**: 取得系統中所有使用者的列表

**查詢參數**:
- `limit` (可選): 結果限制，預設 100，最大 500
- `offset` (可選): 偏移量，預設 0

**範例請求**:
```bash
curl "http://localhost:5000/api/users?limit=10&offset=0"
```

**範例回應**:
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "username": "test_user1",
        "first_seen": "2024-01-01T00:00:00",
        "last_seen": "2025-06-19T13:20:53.709313"
      },
      {
        "username": "test_user2",
        "first_seen": "2024-01-01T00:00:00",
        "last_seen": "2025-06-19T13:20:54.013448"
      }
    ],
    "count": 2,
    "limit": 100,
    "offset": 0
  },
  "query_info": {
    "timestamp": "2025-06-19 13:24:22"
  }
}
```

### 2. 列出所有 IP 位址

**端點**: `GET /api/ips`

**描述**: 取得系統中所有 IP 位址的列表

**查詢參數**:
- `limit` (可選): 結果限制，預設 100，最大 500
- `offset` (可選): 偏移量，預設 0

**範例請求**:
```bash
curl "http://localhost:5000/api/ips?limit=10&offset=0"
```

**範例回應**:
```json
{
  "success": true,
  "data": {
    "ips": [
      {
        "address": "*************",
        "first_seen": "2024-01-01T00:00:00",
        "last_seen": "2025-06-19T13:20:54.318867"
      }
    ],
    "count": 1,
    "limit": 100,
    "offset": 0
  },
  "query_info": {
    "timestamp": "2025-06-19 13:24:29"
  }
}
```

### 3. 使用者 IP 分析

**端點**: `GET /api/user-ip-analysis`

**描述**: 分析特定使用者的 IP 使用情況，並找出使用相同 IP 的其他使用者

**查詢參數**:
- `username` (必需): 要分析的使用者名稱

**範例請求**:
```bash
curl "http://localhost:5000/api/user-ip-analysis?username=test_user1"
```

**範例回應**:
```json
{
  "success": true,
  "data": {
    "username": "test_user1",
    "user_ips": [
      {
        "ip": "*************",
        "action": "post",
        "date": "2024-01-01"
      }
    ],
    "ip_analysis": [
      {
        "ip_address": "*************",
        "total_users": 1,
        "other_users": [],
        "other_users_count": 0,
        "posts_from_ip": [
          {
            "post_id": "Test.M.1234567890",
            "title": "測試文章",
            "author": "test_user1",
            "date": "2024-01-01"
          }
        ],
        "posts_count": 1,
        "all_users_detail": [
          {
            "username": "test_user1",
            "action": "post",
            "date": "2024-01-01"
          }
        ]
      }
    ],
    "related_users": [],
    "summary": {
      "total_ips_used": 1,
      "total_related_users": 0,
      "suspicious_ips": 0,
      "risk_level": "low",
      "analysis_timestamp": "2025-06-19T13:24:36.562229"
    }
  },
  "query_info": {
    "username": "test_user1",
    "timestamp": "2025-06-19 13:24:36"
  }
}
```

**風險等級說明**:
- `low`: 沒有可疑的 IP 共享行為
- `medium`: 有 1-2 個 IP 與其他使用者共享
- `high`: 有 3 個以上 IP 與其他使用者共享

## 其他現有 API

### 系統狀態

- `GET /api/system/health` - 健康檢查
- `GET /api/system/status` - 系統狀態

### 圖形查詢

- `GET /api/graph-stats` - 圖形統計資訊
- `GET /api/user-links?username=<user>` - 使用者關聯查詢
- `GET /api/ip-users?ip=<ip_address>` - IP 使用者查詢
- `GET /api/post-interactions?post_id=<post_id>` - 文章互動查詢
- `GET /api/search-users?pattern=<pattern>` - 搜尋使用者
- `GET /api/popular-boards` - 熱門看板

## 錯誤處理

所有 API 都會返回統一的錯誤格式：

```json
{
  "success": false,
  "error": "錯誤訊息",
  "query_info": {
    "timestamp": "2025-06-19 13:24:36"
  }
}
```

常見錯誤碼：
- `400`: 請求參數錯誤
- `500`: 內部伺服器錯誤

## 使用範例

### Python 範例

```python
import requests

# 列出所有使用者
response = requests.get('http://localhost:5000/api/users')
users = response.json()['data']['users']

# 分析特定使用者的 IP
response = requests.get('http://localhost:5000/api/user-ip-analysis?username=test_user1')
analysis = response.json()['data']

print(f"使用者 {analysis['username']} 使用了 {analysis['summary']['total_ips_used']} 個 IP")
```

### JavaScript 範例

```javascript
// 列出所有 IP
fetch('http://localhost:5000/api/ips')
  .then(response => response.json())
  .then(data => {
    console.log('IP 列表:', data.data.ips);
  });

// 使用者 IP 分析
fetch('http://localhost:5000/api/user-ip-analysis?username=test_user1')
  .then(response => response.json())
  .then(data => {
    const analysis = data.data;
    console.log(`風險等級: ${analysis.summary.risk_level}`);
  });
```

## 注意事項

1. **效能考量**: 大量資料查詢時建議使用分頁參數 (`limit` 和 `offset`)
2. **快取**: API 回應未實作快取，每次請求都會查詢資料庫
3. **並發**: 支援多個並發請求
4. **CORS**: 已啟用 CORS 支援，可從瀏覽器直接呼叫

## 部署注意事項

在生產環境中：
1. 使用 HTTPS
2. 實作 API 金鑰驗證
3. 設定適當的速率限制
4. 啟用日誌記錄和監控
