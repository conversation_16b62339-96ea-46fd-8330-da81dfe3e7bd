#!/usr/bin/env python3
"""
[?] Cosmos DB [?]
"""

import os
from config import Config

def test_basic_connection():
    """[?]"""
    print("=== Cosmos DB [?] ===")
    
    # [?]
    print(f"[?]: {Config.COSMOS_DB_ENDPOINT}")
    print(f"[?]: {Config.COSMOS_DB_DATABASE}")
    print(f"[?]: {Config.COSMOS_DB_COLLECTION}")
    print(f"[?]: {'[?]' if Config.COSMOS_DB_KEY else '[?]'}")
    
    if not Config.COSMOS_DB_ENDPOINT or not Config.COSMOS_DB_KEY:
        print("[ERROR] [?]")
        return False
    
    try:
        # [?]
        print("\n1. [?] gremlinpython [?]...")
        from gremlin_python.driver import client, serializer
        print("[OK] gremlinpython [?]")
        
        print("\n2. [?]...")
        
        # [?]1[?] wss [?] aiohttp [?]
        try:
            endpoint = Config.COSMOS_DB_ENDPOINT
            if not endpoint.startswith('wss://'):
                if endpoint.startswith('https://'):
                    endpoint = endpoint.replace('https://', 'wss://')
                else:
                    endpoint = f"wss://{endpoint}"

            if not endpoint.endswith(':443/'):
                if endpoint.endswith('/'):
                    endpoint = endpoint[:-1] + ':443/'
                else:
                    endpoint = endpoint + ':443/'

            print(f"   [?]: {endpoint}")

            # [?] aiohttp [?]
            try:
                from gremlin_python.driver.aiohttp.transport import AiohttpTransport
                transport_factory = lambda: AiohttpTransport()

                gremlin_client = client.Client(
                    endpoint,
                    'g',
                    username=f"/dbs/{Config.COSMOS_DB_DATABASE}/colls/{Config.COSMOS_DB_COLLECTION}",
                    password=Config.COSMOS_DB_KEY,
                    message_serializer=serializer.GraphSONSerializersV2d0(),
                    transport_factory=transport_factory
                )
            except ImportError:
                print("   [?] AiohttpTransport[?]")
                gremlin_client = client.Client(
                    endpoint,
                    'g',
                    username=f"/dbs/{Config.COSMOS_DB_DATABASE}/colls/{Config.COSMOS_DB_COLLECTION}",
                    password=Config.COSMOS_DB_KEY,
                    message_serializer=serializer.GraphSONSerializersV2d0()
                )
            
            print("[OK] [?]")
            
            print("\n3. [?]...")
            result = gremlin_client.submit("g.V().count()").all().result()
            print(f"[OK] [?]: {result[0] if result else 0}")
            
            gremlin_client.close()
            return True
            
        except Exception as e:
            print(f"[ERROR] [?]: {e}")
            
            # [?]
            print("\n4. [?]...")
            try:
                # [?] https [?]
                https_endpoint = Config.COSMOS_DB_ENDPOINT.replace('wss://', 'https://')
                print(f"   [?] HTTPS [?]: {https_endpoint}")
                
                gremlin_client = client.Client(
                    https_endpoint,
                    'g',
                    username=f"/dbs/{Config.COSMOS_DB_DATABASE}/colls/{Config.COSMOS_DB_COLLECTION}",
                    password=Config.COSMOS_DB_KEY
                )
                
                result = gremlin_client.submit("g.V().count()").all().result()
                print(f"[OK] HTTPS [?]: {result[0] if result else 0}")
                
                gremlin_client.close()
                return True
                
            except Exception as e2:
                print(f"[ERROR] HTTPS [?]: {e2}")
                return False
    
    except ImportError as e:
        print(f"[ERROR] [?]: {e}")
        return False
    except Exception as e:
        print(f"[ERROR] [?]: {e}")
        return False

def test_create_database():
    """[?]"""
    print("\n=== [?] ===")
    
    try:
        from gremlin_python.driver import client, serializer
        
        # [?]
        endpoint = Config.COSMOS_DB_ENDPOINT
        if endpoint.startswith('wss://'):
            endpoint = endpoint.replace('wss://', 'https://')
        
        print(f"[?]: {endpoint}")
        
        # [?] Azure SDK [?]
        # gremlinpython [?]
        print("[WARN] [?] Azure [?] Azure CLI")
        print("   [?] Azure [?]")
        print(f"   - [?]: {Config.COSMOS_DB_DATABASE}")
        print(f"   - [?]: {Config.COSMOS_DB_COLLECTION}")
        
        return True
        
    except Exception as e:
        print(f"[ERROR] [?]: {e}")
        return False

def main():
    """[?]"""
    print("Cosmos DB [?]")
    print("=" * 40)
    
    # [?]
    connection_ok = test_basic_connection()
    
    if connection_ok:
        print("\n[SUCCESS] Cosmos DB [?]")
        
        # [?]
        test_create_database()
        
        return 0
    else:
        print("\n[ERROR] Cosmos DB [?]")
        print("\n[?]")
        print("1. [?] COSMOS_DB_ENDPOINT [?]")
        print("2. [?] COSMOS_DB_KEY [?]")
        print("3. [?] Cosmos DB [?] Gremlin API")
        print("4. [?]")
        print("5. [?]")
        
        return 1

if __name__ == "__main__":
    exit(main())
