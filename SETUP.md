# PTT 自動登入系統 - 設定指南

## 🚀 快速設定步驟

### 1. 設定 PTT 帳號密碼

編輯 `.env` 檔案，將以下兩行：

```env
PTT_USERNAME=your_ptt_username
PTT_PASSWORD=your_ptt_password
```

改為您的實際 PTT 帳號和密碼：

```env
PTT_USERNAME=您的PTT帳號
PTT_PASSWORD=您的PTT密碼
```

### 2. 重新啟動應用程式

設定完成後，請重新啟動 Flask 應用程式：

1. 在終端機中按 `Ctrl+C` 停止當前運行的應用程式
2. 重新執行：`py app.py`
3. 開啟瀏覽器訪問：http://127.0.0.1:5000

### 3. 測試登入

在 Web 介面中：

1. 查看「系統配置」區域，確認 PTT 使用者顯示為您的帳號（會隱藏部分字元）
2. 點擊「開始測試登入」按鈕
3. 查看測試結果

## 🔧 已修正的問題

我已經修正了以下 PyPtt API 相容性問題：

1. **登入方法參數**：
   - 舊版：`username=`, `password=`
   - 新版：`ptt_id=`, `ptt_password=`

2. **連接建立**：
   - 移除了不支援的 `host` 和 `port` 參數
   - 簡化為基本的 API 初始化

3. **連接關閉**：
   - 移除了不存在的 `close()` 方法
   - 使用 `logout()` 來清理連接

## 📋 測試流程

系統會執行以下測試步驟：

1. **連接 PTT**：建立 PyPtt API 實例
2. **登入測試**：使用您的帳密進行登入
3. **驗證成功**：確認登入狀態
4. **清理連接**：自動登出並清理資源

## ⚠️ 注意事項

### 安全性
- `.env` 檔案已加入 `.gitignore`，不會被提交到版本控制
- Web 介面會隱藏密碼顯示
- 日誌不會記錄完整密碼

### PTT 帳號要求
- 確保帳號密碼正確
- 如果有其他 PTT 連線，系統會自動踢掉（`kick_other_login=True`）
- 建議使用測試用的 PTT 帳號

### 常見問題
1. **「帳號或密碼錯誤」**：請檢查 `.env` 檔案中的帳密是否正確
2. **「連接失敗」**：請檢查網路連接和 PTT 伺服器狀態
3. **「環境變數未設定」**：請確認 `.env` 檔案存在且格式正確

## 🎯 成功標準

當您看到以下訊息時，表示設定成功：

```
✓ PTT 連接成功
✓ PTT 登入成功
✓ 使用者資訊: 您的帳號
```

## 📞 需要協助？

如果遇到問題：

1. 檢查 `logs/` 目錄下的日誌檔案
2. 執行 `py test_system.py` 進行系統診斷
3. 確認 `.env` 檔案格式正確
4. 嘗試手動登入 PTT 確認帳密無誤

---

**下一步**：設定完成後，您就可以開始測試 PTT 登入功能，並為後續的自動發文功能做準備！
