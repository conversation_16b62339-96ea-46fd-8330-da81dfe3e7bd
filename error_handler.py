"""
PTT 自動登入系統 - 統一錯誤處理機制
"""

from flask import jsonify, Response
from logger import ptt_logger
import json
from enum import Enum
from typing import Dict, Any, Optional, Union


class ErrorCode(Enum):
    """錯誤代碼枚舉"""
    # 通用錯誤 (1000-1999)
    UNKNOWN_ERROR = 1000
    INVALID_REQUEST = 1001
    MISSING_PARAMETER = 1002
    INVALID_PARAMETER = 1003
    INTERNAL_ERROR = 1004
    
    # 認證和授權錯誤 (2000-2999)
    AUTHENTICATION_FAILED = 2001
    AUTHORIZATION_FAILED = 2002
    INVALID_CREDENTIALS = 2003
    SESSION_EXPIRED = 2004
    
    # PTT 相關錯誤 (3000-3999)
    PTT_CONNECTION_FAILED = 3001
    PTT_LOGIN_FAILED = 3002
    PTT_BOARD_NOT_FOUND = 3003
    PTT_POST_NOT_FOUND = 3004
    PTT_CRAWL_FAILED = 3005
    
    # 資料庫錯誤 (4000-4999)
    DATABASE_CONNECTION_FAILED = 4001
    DATABASE_QUERY_FAILED = 4002
    DATABASE_WRITE_FAILED = 4003
    DATABASE_NOT_CONFIGURED = 4004
    
    # 配置錯誤 (5000-5999)
    CONFIG_MISSING = 5001
    CONFIG_INVALID = 5002
    PTT_CONFIG_MISSING = 5003
    COSMOS_CONFIG_MISSING = 5004
    
    # 業務邏輯錯誤 (6000-6999)
    USER_NOT_FOUND = 6001
    POST_NOT_FOUND = 6002
    IP_INVALID = 6003
    BOARD_INVALID = 6004
    QUERY_LIMIT_EXCEEDED = 6005


class ErrorMessages:
    """錯誤訊息對應"""
    
    MESSAGES = {
        ErrorCode.UNKNOWN_ERROR: "發生未知錯誤",
        ErrorCode.INVALID_REQUEST: "請求格式不正確",
        ErrorCode.MISSING_PARAMETER: "缺少必要參數",
        ErrorCode.INVALID_PARAMETER: "參數格式不正確",
        ErrorCode.INTERNAL_ERROR: "內部伺服器錯誤",
        
        ErrorCode.AUTHENTICATION_FAILED: "認證失敗",
        ErrorCode.AUTHORIZATION_FAILED: "授權失敗",
        ErrorCode.INVALID_CREDENTIALS: "帳號或密碼不正確",
        ErrorCode.SESSION_EXPIRED: "會話已過期",
        
        ErrorCode.PTT_CONNECTION_FAILED: "無法連接到 PTT 伺服器",
        ErrorCode.PTT_LOGIN_FAILED: "PTT 登入失敗",
        ErrorCode.PTT_BOARD_NOT_FOUND: "找不到指定的看板",
        ErrorCode.PTT_POST_NOT_FOUND: "找不到指定的文章",
        ErrorCode.PTT_CRAWL_FAILED: "爬文失敗",
        
        ErrorCode.DATABASE_CONNECTION_FAILED: "無法連接到資料庫",
        ErrorCode.DATABASE_QUERY_FAILED: "資料庫查詢失敗",
        ErrorCode.DATABASE_WRITE_FAILED: "資料庫寫入失敗",
        ErrorCode.DATABASE_NOT_CONFIGURED: "資料庫未正確配置",
        
        ErrorCode.CONFIG_MISSING: "缺少必要配置",
        ErrorCode.CONFIG_INVALID: "配置格式不正確",
        ErrorCode.PTT_CONFIG_MISSING: "PTT 配置不完整",
        ErrorCode.COSMOS_CONFIG_MISSING: "Cosmos DB 配置不完整",
        
        ErrorCode.USER_NOT_FOUND: "找不到指定的使用者",
        ErrorCode.POST_NOT_FOUND: "找不到指定的文章",
        ErrorCode.IP_INVALID: "IP 位址格式不正確",
        ErrorCode.BOARD_INVALID: "看板名稱不正確",
        ErrorCode.QUERY_LIMIT_EXCEEDED: "查詢結果超過限制",
    }
    
    @classmethod
    def get_message(cls, error_code: ErrorCode) -> str:
        """取得錯誤訊息"""
        return cls.MESSAGES.get(error_code, "未知錯誤")


class APIError(Exception):
    """API 錯誤基類"""
    
    def __init__(
        self, 
        error_code: ErrorCode, 
        message: Optional[str] = None,
        details: Optional[Union[str, list, dict]] = None,
        status_code: int = 400
    ):
        self.error_code = error_code
        self.message = message or ErrorMessages.get_message(error_code)
        self.details = details
        self.status_code = status_code
        super().__init__(self.message)


class ErrorHandler:
    """統一錯誤處理器"""
    
    @staticmethod
    def create_error_response(
        error_code: ErrorCode,
        message: Optional[str] = None,
        details: Optional[Union[str, list, dict]] = None,
        status_code: int = 400,
        request_id: Optional[str] = None
    ) -> Response:
        """建立標準錯誤回應"""
        
        error_message = message or ErrorMessages.get_message(error_code)
        
        response_data = {
            "success": False,
            "error": {
                "code": error_code.value,
                "type": error_code.name,
                "message": error_message,
                "timestamp": ptt_logger.get_timestamp()
            }
        }
        
        # 添加詳細資訊
        if details:
            response_data["error"]["details"] = details
            
        # 添加請求ID（用於追蹤）
        if request_id:
            response_data["error"]["request_id"] = request_id
        
        # 記錄錯誤
        ptt_logger.error(f"API錯誤 [{error_code.value}]: {error_message}")
        if details:
            ptt_logger.error(f"錯誤詳情: {details}")
        
        return Response(
            json.dumps(response_data, ensure_ascii=False, indent=2),
            status=status_code,
            mimetype='application/json; charset=utf-8'
        )
    
    @staticmethod
    def handle_exception(e: Exception, request_id: Optional[str] = None) -> Response:
        """處理異常並返回適當的錯誤回應"""
        
        if isinstance(e, APIError):
            return ErrorHandler.create_error_response(
                e.error_code,
                e.message,
                e.details,
                e.status_code,
                request_id
            )
        
        # 處理常見的 Python 異常
        if isinstance(e, ValueError):
            return ErrorHandler.create_error_response(
                ErrorCode.INVALID_PARAMETER,
                f"參數值錯誤: {str(e)}",
                status_code=400,
                request_id=request_id
            )
        
        if isinstance(e, KeyError):
            return ErrorHandler.create_error_response(
                ErrorCode.MISSING_PARAMETER,
                f"缺少必要參數: {str(e)}",
                status_code=400,
                request_id=request_id
            )
        
        if isinstance(e, ConnectionError):
            return ErrorHandler.create_error_response(
                ErrorCode.DATABASE_CONNECTION_FAILED,
                "連接失敗",
                str(e),
                status_code=503,
                request_id=request_id
            )
        
        # 未知錯誤
        ptt_logger.error(f"未處理的異常: {type(e).__name__}: {str(e)}")
        return ErrorHandler.create_error_response(
            ErrorCode.UNKNOWN_ERROR,
            "系統發生未預期的錯誤",
            str(e),
            status_code=500,
            request_id=request_id
        )
    
    @staticmethod
    def create_success_response(
        data: Any,
        message: Optional[str] = None,
        meta: Optional[Dict] = None
    ) -> Response:
        """建立標準成功回應"""
        
        response_data = {
            "success": True,
            "data": data,
            "timestamp": ptt_logger.get_timestamp()
        }
        
        if message:
            response_data["message"] = message
            
        if meta:
            response_data["meta"] = meta
        
        return Response(
            json.dumps(response_data, ensure_ascii=False, indent=2),
            status=200,
            mimetype='application/json; charset=utf-8'
        )


# 常用的錯誤回應快捷函數
def missing_parameter_error(parameter_name: str) -> Response:
    """缺少參數錯誤"""
    return ErrorHandler.create_error_response(
        ErrorCode.MISSING_PARAMETER,
        f"缺少必要參數: {parameter_name}",
        {"parameter": parameter_name}
    )


def invalid_parameter_error(parameter_name: str, reason: str = "") -> Response:
    """無效參數錯誤"""
    message = f"參數 '{parameter_name}' 格式不正確"
    if reason:
        message += f": {reason}"
    
    return ErrorHandler.create_error_response(
        ErrorCode.INVALID_PARAMETER,
        message,
        {"parameter": parameter_name, "reason": reason}
    )


def database_error(operation: str, details: str = "") -> Response:
    """資料庫錯誤"""
    return ErrorHandler.create_error_response(
        ErrorCode.DATABASE_QUERY_FAILED,
        f"資料庫{operation}失敗",
        details,
        status_code=503
    )


def ptt_error(operation: str, details: str = "") -> Response:
    """PTT 操作錯誤"""
    return ErrorHandler.create_error_response(
        ErrorCode.PTT_CRAWL_FAILED,
        f"PTT {operation}失敗",
        details,
        status_code=503
    )


def config_error(config_type: str, missing_items: list = None) -> Response:
    """配置錯誤"""
    error_code = ErrorCode.CONFIG_MISSING
    if config_type.lower() == 'ptt':
        error_code = ErrorCode.PTT_CONFIG_MISSING
    elif config_type.lower() == 'cosmos':
        error_code = ErrorCode.COSMOS_CONFIG_MISSING
    
    return ErrorHandler.create_error_response(
        error_code,
        f"{config_type} 配置不完整",
        {"missing_items": missing_items or []},
        status_code=503
    )
