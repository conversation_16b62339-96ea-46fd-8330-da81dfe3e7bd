#!/usr/bin/env python3
"""
簡化版圖形查詢模組
基於成功的分層查詢邏輯，提供穩定的關係查詢功能
"""

from typing import Dict, List
from datetime import datetime
from gremlin_python.driver import client, serializer
from gremlin_python.process.graph_traversal import __
from config import Config
from logger import ptt_logger

class SimpleGraphQuery:
    """簡化版圖形查詢類"""
    
    def __init__(self):
        """初始化圖形查詢器"""
        self.client = None
        self.is_connected = False
        self._connect()
    
    def _connect(self):
        """連接到 Cosmos DB"""
        try:
            endpoint = Config.COSMOS_DB_ENDPOINT
            key = Config.COSMOS_DB_KEY
            database = Config.COSMOS_DB_DATABASE
            collection = Config.COSMOS_DB_COLLECTION

            # 處理連接字符串格式
            if endpoint.startswith('wss://'):
                connection_string = endpoint
            elif endpoint.startswith('https://'):
                connection_string = endpoint.replace('https://', 'wss://').replace(':443/', ':443/gremlin')
            else:
                connection_string = f"wss://{endpoint}:443/gremlin"
            
            self.client = client.Client(
                connection_string,
                'g',
                username=f"/dbs/{database}/colls/{collection}",
                password=key,
                message_serializer=serializer.GraphSONSerializersV2d0()
            )
            
            # 測試連接
            test_result = self.client.submit("g.V().limit(1)").all().result()
            self.is_connected = True
            ptt_logger.info("簡化圖形查詢器 Cosmos DB 連接初始化成功")
            
        except Exception as e:
            ptt_logger.error(f"簡化圖形查詢器 Cosmos DB 連接失敗: {e}")
            self.is_connected = False
    
    def close(self):
        """關閉連接"""
        if self.client:
            self.client.close()
            self.is_connected = False
            ptt_logger.info("簡化圖形查詢器 Cosmos DB 連接已關閉")
    
    def _extract_value(self, value):
        """從Gremlin返回值中提取實際值"""
        if isinstance(value, list) and len(value) > 0:
            return value[0]
        return value if value is not None else ''
    
    def query_user_relationships(self, userid: str, max_depth: int = 3) -> Dict:
        """查詢使用者關係網絡
        
        Args:
            userid: 使用者ID
            max_depth: 最大查詢深度
            
        Returns:
            dict: 關係網絡資料
        """
        if not self.is_connected:
            return {'error': '未連接到圖形資料庫'}
        
        try:
            ptt_logger.log_operation_start(f"查詢使用者關係: {userid}")
            
            result = {
                'user': userid,
                'timestamp': datetime.now().isoformat(),
                'relationships': [],
                'network_data': {
                    'nodes': [],
                    'edges': []
                },
                'statistics': {
                    'total_connections': 0,
                    'layer_counts': {}
                }
            }
            
            # 添加中心使用者節點
            result['network_data']['nodes'].append({
                'id': userid,
                'label': userid,
                'type': 'user',
                'layer': 0,
                'is_center': True
            })
            
            # 第一層：推文互動關係
            layer1_connections = self._query_comment_relationships(userid)
            result['relationships'].extend(layer1_connections)
            
            # 第二層：IP共用關係
            if max_depth >= 2:
                layer2_connections = self._query_ip_relationships(userid)
                result['relationships'].extend(layer2_connections)
            
            # 移除同板發文關係 - 因為同板發文不代表互相認識
            # 真正的關聯應該是有互動的文章
            # if max_depth >= 3:
            #     layer3_connections = self._query_board_relationships(userid)
            #     result['relationships'].extend(layer3_connections)
            
            # 建構網絡圖資料
            self._build_network_data(result, userid)
            
            # 計算統計資訊
            self._calculate_statistics(result)
            
            ptt_logger.log_operation_end(f"查詢使用者關係: {userid}", True, 
                                       f"關聯: {result['statistics']['total_connections']}")
            
            return result
            
        except Exception as e:
            ptt_logger.log_operation_end(f"查詢使用者關係: {userid}", False, f"錯誤: {e}")
            return {'error': str(e)}
    
    def _query_comment_relationships(self, userid: str) -> List[Dict]:
        """查詢推文互動關係 - 真正有互動的關聯"""
        connections = []

        try:
            # 1. 我推了誰的文章 (真正的互動)
            ptt_logger.info(f"查詢 {userid} 推了誰的文章")
            my_comments_query = f"""
            g.V().hasLabel('user').has('userid', '{userid}')
            .out('commented').hasLabel('post')
            .in('posted').hasLabel('user')
            .dedup()
            .valueMap('userid', 'nickname')
            """

            my_comments_result = self.client.submit(my_comments_query).all().result()

            for user_data in my_comments_result:
                target_userid = self._extract_value(user_data.get('userid', ''))
                target_nickname = self._extract_value(user_data.get('nickname', ''))

                if target_userid and target_userid != userid:
                    # 查詢具體互動的文章數量
                    interaction_count = self._count_interactions(userid, target_userid, 'commented_their_post')

                    connections.append({
                        'target_userid': target_userid,
                        'target_nickname': target_nickname,
                        'type': 'i_commented_their_post',
                        'role': f'我推他的文 ({interaction_count}篇)',
                        'layer': 1,
                        'strength': min(1.0, 0.5 + interaction_count * 0.1),
                        'interaction_count': interaction_count
                    })

            # 2. 誰推了我的文章 (真正的互動)
            ptt_logger.info(f"查詢誰推了 {userid} 的文章")
            my_posts_commenters_query = f"""
            g.V().hasLabel('user').has('userid', '{userid}')
            .out('posted').hasLabel('post')
            .in('commented').hasLabel('user')
            .dedup()
            .valueMap('userid', 'nickname')
            """

            my_posts_commenters_result = self.client.submit(my_posts_commenters_query).all().result()

            for user_data in my_posts_commenters_result:
                target_userid = self._extract_value(user_data.get('userid', ''))
                target_nickname = self._extract_value(user_data.get('nickname', ''))

                if target_userid and target_userid != userid:
                    # 查詢具體互動的文章數量
                    interaction_count = self._count_interactions(target_userid, userid, 'commented_my_post')

                    connections.append({
                        'target_userid': target_userid,
                        'target_nickname': target_nickname,
                        'type': 'they_commented_my_post',
                        'role': f'他推我的文 ({interaction_count}篇)',
                        'layer': 1,
                        'strength': min(1.0, 0.6 + interaction_count * 0.1),
                        'interaction_count': interaction_count
                    })

            # 3. 互相推文的關係 (雙向互動，關係更強)
            ptt_logger.info(f"查詢與 {userid} 互相推文的使用者")
            mutual_interactions = self._find_mutual_interactions(userid, connections)
            connections.extend(mutual_interactions)

        except Exception as e:
            ptt_logger.error(f"查詢推文關係失敗: {e}")

        return connections
    
    def _query_ip_relationships(self, userid: str) -> List[Dict]:
        """查詢IP共用關係"""
        connections = []
        
        try:
            shared_ip_query = f"""
            g.V().hasLabel('user').has('userid', '{userid}')
            .out('used_ip').hasLabel('ip')
            .in('used_ip').hasLabel('user')
            .dedup()
            .valueMap('userid', 'nickname')
            """
            
            shared_ip_result = self.client.submit(shared_ip_query).all().result()
            
            for user_data in shared_ip_result:
                target_userid = self._extract_value(user_data.get('userid', ''))
                target_nickname = self._extract_value(user_data.get('nickname', ''))
                
                if target_userid and target_userid != userid:
                    connections.append({
                        'target_userid': target_userid,
                        'target_nickname': target_nickname,
                        'type': 'shared_ip',
                        'role': 'IP共用者',
                        'layer': 2,
                        'strength': 0.9
                    })
            
        except Exception as e:
            ptt_logger.error(f"查詢IP關係失敗: {e}")
        
        return connections
    
    def _query_board_relationships(self, userid: str) -> List[Dict]:
        """查詢同板發文關係"""
        connections = []
        
        try:
            # 先取得使用者發文的看板
            boards_query = f"""
            g.V().hasLabel('user').has('userid', '{userid}')
            .out('posted').hasLabel('post')
            .values('board').dedup()
            """
            
            boards_result = self.client.submit(boards_query).all().result()
            
            # 對每個看板查詢其他發文者
            for board in boards_result:
                board_users_query = f"""
                g.V().hasLabel('post').has('board', '{board}')
                .in('posted').hasLabel('user')
                .dedup()
                .valueMap('userid', 'nickname')
                """
                
                board_users_result = self.client.submit(board_users_query).all().result()
                
                for user_data in board_users_result:
                    target_userid = self._extract_value(user_data.get('userid', ''))
                    target_nickname = self._extract_value(user_data.get('nickname', ''))
                    
                    if target_userid and target_userid != userid:
                        connections.append({
                            'target_userid': target_userid,
                            'target_nickname': target_nickname,
                            'type': 'same_board_posting',
                            'role': f'同板發文({board})',
                            'layer': 3,
                            'strength': 0.4
                        })
            
        except Exception as e:
            ptt_logger.error(f"查詢看板關係失敗: {e}")
        
        return connections
    
    def _build_network_data(self, result: Dict, center_userid: str):
        """建構網絡圖資料"""
        nodes = result['network_data']['nodes']
        edges = result['network_data']['edges']
        
        # 去重並添加節點
        added_nodes = {center_userid}
        
        for rel in result['relationships']:
            target_userid = rel['target_userid']
            
            if target_userid not in added_nodes:
                target_nickname = rel.get('target_nickname', '')
                display_name = f"{target_userid} ({target_nickname})" if target_nickname else target_userid
                
                nodes.append({
                    'id': target_userid,
                    'label': display_name,
                    'type': 'user',
                    'layer': rel['layer'],
                    'is_center': False
                })
                added_nodes.add(target_userid)
            
            # 添加邊
            edges.append({
                'id': f"{center_userid}-{target_userid}-{rel['type']}",
                'source': center_userid,
                'target': target_userid,
                'type': rel['type'],
                'label': rel['role'],
                'strength': rel['strength'],
                'layer': rel['layer']
            })
    
    def _calculate_statistics(self, result: Dict):
        """計算統計資訊"""
        stats = result['statistics']
        relationships = result['relationships']
        
        stats['total_connections'] = len(relationships)
        
        # 按層統計
        for rel in relationships:
            layer = rel['layer']
            if layer not in stats['layer_counts']:
                stats['layer_counts'][layer] = 0
            stats['layer_counts'][layer] += 1
        
        # 按類型統計
        type_counts = {}
        for rel in relationships:
            rel_type = rel['type']
            if rel_type not in type_counts:
                type_counts[rel_type] = 0
            type_counts[rel_type] += 1
        
        stats['type_counts'] = type_counts
        stats['node_count'] = len(result['network_data']['nodes'])
        stats['edge_count'] = len(result['network_data']['edges'])

    def _count_interactions(self, commenter_userid: str, author_userid: str, interaction_type: str) -> int:
        """計算兩個使用者之間的互動次數"""
        try:
            # 使用分步查詢避免複雜的 where 語句
            if interaction_type == 'commented_their_post':
                # 先找到 author 的所有文章
                author_posts_query = f"""
                g.V().hasLabel('user').has('userid', '{author_userid}')
                .out('posted').hasLabel('post')
                .values('post_id')
                """

                author_posts_result = self.client.submit(author_posts_query).all().result()

                if not author_posts_result:
                    return 0

                # 計算 commenter 推了這些文章中的多少篇
                post_ids = [str(post_id) for post_id in author_posts_result]

                # 逐個查詢每篇文章，避免複雜語法
                total_count = 0
                for post_id in post_ids:
                    count_query = f"""
                    g.V().hasLabel('user').has('userid', '{commenter_userid}')
                    .out('commented').hasLabel('post')
                    .has('post_id', '{post_id}')
                    .count()
                    """

                    result = self.client.submit(count_query).all().result()
                    total_count += result[0] if result else 0

                return total_count

            else:  # commented_my_post - 同樣的邏輯
                return self._count_interactions(commenter_userid, author_userid, 'commented_their_post')

        except Exception as e:
            ptt_logger.error(f"計算互動次數失敗: {e}")
            return 1  # 預設值

    def _find_mutual_interactions(self, userid: str, existing_connections: List[Dict]) -> List[Dict]:
        """找出互相推文的關係"""
        mutual_connections = []

        try:
            # 建立已知關聯的字典
            i_commented = set()  # 我推了誰
            they_commented = set()  # 誰推了我

            for conn in existing_connections:
                target_userid = conn['target_userid']
                if conn['type'] == 'i_commented_their_post':
                    i_commented.add(target_userid)
                elif conn['type'] == 'they_commented_my_post':
                    they_commented.add(target_userid)

            # 找出互相推文的使用者
            mutual_users = i_commented.intersection(they_commented)

            for target_userid in mutual_users:
                # 找到對應的連接資訊
                my_comment_conn = next((c for c in existing_connections
                                      if c['target_userid'] == target_userid and c['type'] == 'i_commented_their_post'), None)
                their_comment_conn = next((c for c in existing_connections
                                         if c['target_userid'] == target_userid and c['type'] == 'they_commented_my_post'), None)

                if my_comment_conn and their_comment_conn:
                    total_interactions = my_comment_conn.get('interaction_count', 0) + their_comment_conn.get('interaction_count', 0)

                    mutual_connections.append({
                        'target_userid': target_userid,
                        'target_nickname': my_comment_conn['target_nickname'],
                        'type': 'mutual_interaction',
                        'role': f'互相推文 (共{total_interactions}次)',
                        'layer': 1,
                        'strength': min(1.0, 0.8 + total_interactions * 0.05),
                        'interaction_count': total_interactions
                    })

        except Exception as e:
            ptt_logger.error(f"查詢互相推文關係失敗: {e}")

        return mutual_connections
