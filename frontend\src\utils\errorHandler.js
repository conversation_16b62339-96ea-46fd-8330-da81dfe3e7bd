/**
 * 前端錯誤處理工具
 * 統一處理API錯誤回應和用戶提示
 */

import Swal from 'sweetalert2'

// 錯誤類型對應的圖示和顏色
const ERROR_TYPES = {
  error: {
    icon: 'error',
    color: '#d33',
    title: '錯誤'
  },
  warning: {
    icon: 'warning', 
    color: '#f39c12',
    title: '警告'
  },
  info: {
    icon: 'info',
    color: '#3085d6',
    title: '提示'
  },
  success: {
    icon: 'success',
    color: '#28a745',
    title: '成功'
  }
}

// 錯誤代碼對應的用戶友好訊息
const ERROR_CODE_MESSAGES = {
  // 通用錯誤 (1000-1999)
  1000: '系統發生未知錯誤，請稍後再試',
  1001: '請求格式不正確，請檢查輸入資料',
  1002: '缺少必要參數，請完整填寫所有必填欄位',
  1003: '參數格式不正確，請檢查輸入格式',
  1004: '伺服器內部錯誤，請聯繫系統管理員',
  
  // 認證和授權錯誤 (2000-2999)
  2001: '認證失敗，請重新登入',
  2002: '您沒有權限執行此操作',
  2003: '帳號或密碼不正確',
  2004: '會話已過期，請重新登入',
  
  // PTT 相關錯誤 (3000-3999)
  3001: '無法連接到 PTT 伺服器，請檢查網路連線',
  3002: 'PTT 登入失敗，請檢查帳號密碼',
  3003: '找不到指定的看板',
  3004: '找不到指定的文章',
  3005: '爬文失敗，請稍後再試',
  
  // 資料庫錯誤 (4000-4999)
  4001: '無法連接到資料庫，請稍後再試',
  4002: '資料查詢失敗，請稍後再試',
  4003: '資料寫入失敗，請稍後再試',
  4004: '資料庫配置不正確，請聯繫系統管理員',
  
  // 配置錯誤 (5000-5999)
  5001: '系統配置不完整，請聯繫系統管理員',
  5002: '系統配置格式不正確，請聯繫系統管理員',
  5003: 'PTT 配置不完整，請設定 PTT 帳號密碼',
  5004: 'Cosmos DB 配置不完整，請設定資料庫連線',
  
  // 業務邏輯錯誤 (6000-6999)
  6001: '找不到指定的使用者',
  6002: '找不到指定的文章',
  6003: 'IP 位址格式不正確',
  6004: '看板名稱不正確',
  6005: '查詢結果超過限制，請縮小查詢範圍'
}

/**
 * 錯誤處理器類別
 */
class ErrorHandler {
  
  /**
   * 處理API錯誤回應
   * @param {Object} error - Axios錯誤物件或API錯誤回應
   * @param {Object} options - 顯示選項
   */
  static handleApiError(error, options = {}) {
    console.error('API錯誤:', error)
    
    let errorInfo = this.parseError(error)
    
    // 合併選項
    const defaultOptions = {
      showAlert: true,
      logError: true,
      title: ERROR_TYPES.error.title,
      icon: ERROR_TYPES.error.icon,
      confirmButtonColor: ERROR_TYPES.error.color
    }
    
    const finalOptions = { ...defaultOptions, ...options }
    
    // 記錄錯誤
    if (finalOptions.logError) {
      console.error('錯誤詳情:', errorInfo)
    }
    
    // 顯示錯誤提示
    if (finalOptions.showAlert) {
      this.showErrorAlert(errorInfo, finalOptions)
    }
    
    return errorInfo
  }
  
  /**
   * 解析錯誤物件
   * @param {Object} error - 錯誤物件
   * @returns {Object} 解析後的錯誤資訊
   */
  static parseError(error) {
    // Axios 錯誤
    if (error.response) {
      const response = error.response
      const data = response.data
      
      // API 標準錯誤格式
      if (data && data.error) {
        return {
          code: data.error.code || 1000,
          type: data.error.type || 'UNKNOWN_ERROR',
          message: data.error.message || '未知錯誤',
          details: data.error.details || null,
          statusCode: response.status,
          timestamp: data.error.timestamp || new Date().toISOString()
        }
      }
      
      // 非標準錯誤格式
      return {
        code: response.status,
        type: 'HTTP_ERROR',
        message: data.message || data.error || `HTTP ${response.status} 錯誤`,
        details: data,
        statusCode: response.status,
        timestamp: new Date().toISOString()
      }
    }
    
    // 網路錯誤
    if (error.request) {
      return {
        code: 0,
        type: 'NETWORK_ERROR',
        message: '網路連線失敗，請檢查網路狀態',
        details: error.message,
        statusCode: 0,
        timestamp: new Date().toISOString()
      }
    }
    
    // 其他錯誤
    return {
      code: 1000,
      type: 'UNKNOWN_ERROR',
      message: error.message || '發生未知錯誤',
      details: error,
      statusCode: 0,
      timestamp: new Date().toISOString()
    }
  }
  
  /**
   * 顯示錯誤提示框
   * @param {Object} errorInfo - 錯誤資訊
   * @param {Object} options - 顯示選項
   */
  static showErrorAlert(errorInfo, options = {}) {
    // 取得用戶友好的錯誤訊息
    const userMessage = ERROR_CODE_MESSAGES[errorInfo.code] || errorInfo.message
    
    // 建立詳細資訊
    let html = `<p>${userMessage}</p>`
    
    if (errorInfo.details && options.showDetails) {
      html += `<hr><small><strong>技術詳情:</strong><br>${JSON.stringify(errorInfo.details, null, 2)}</small>`
    }
    
    if (errorInfo.code && options.showCode) {
      html += `<hr><small><strong>錯誤代碼:</strong> ${errorInfo.code}</small>`
    }
    
    Swal.fire({
      title: options.title || ERROR_TYPES.error.title,
      html: html,
      icon: options.icon || ERROR_TYPES.error.icon,
      confirmButtonColor: options.confirmButtonColor || ERROR_TYPES.error.color,
      confirmButtonText: '確定',
      width: '500px',
      customClass: {
        popup: 'error-popup',
        title: 'error-title',
        content: 'error-content'
      }
    })
  }
  
  /**
   * 顯示成功訊息
   * @param {string} message - 成功訊息
   * @param {Object} options - 顯示選項
   */
  static showSuccess(message, options = {}) {
    const defaultOptions = {
      title: ERROR_TYPES.success.title,
      icon: ERROR_TYPES.success.icon,
      confirmButtonColor: ERROR_TYPES.success.color,
      timer: 3000,
      showConfirmButton: false
    }
    
    const finalOptions = { ...defaultOptions, ...options }
    
    Swal.fire({
      title: finalOptions.title,
      text: message,
      icon: finalOptions.icon,
      timer: finalOptions.timer,
      showConfirmButton: finalOptions.showConfirmButton,
      confirmButtonColor: finalOptions.confirmButtonColor,
      confirmButtonText: '確定'
    })
  }
  
  /**
   * 顯示警告訊息
   * @param {string} message - 警告訊息
   * @param {Object} options - 顯示選項
   */
  static showWarning(message, options = {}) {
    const defaultOptions = {
      title: ERROR_TYPES.warning.title,
      icon: ERROR_TYPES.warning.icon,
      confirmButtonColor: ERROR_TYPES.warning.color
    }
    
    const finalOptions = { ...defaultOptions, ...options }
    
    return Swal.fire({
      title: finalOptions.title,
      text: message,
      icon: finalOptions.icon,
      confirmButtonColor: finalOptions.confirmButtonColor,
      confirmButtonText: '確定'
    })
  }
  
  /**
   * 顯示確認對話框
   * @param {string} message - 確認訊息
   * @param {Object} options - 顯示選項
   */
  static showConfirm(message, options = {}) {
    const defaultOptions = {
      title: '確認操作',
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: '確定',
      cancelButtonText: '取消'
    }
    
    const finalOptions = { ...defaultOptions, ...options }
    
    return Swal.fire({
      title: finalOptions.title,
      text: message,
      icon: finalOptions.icon,
      showCancelButton: finalOptions.showCancelButton,
      confirmButtonColor: finalOptions.confirmButtonColor,
      cancelButtonColor: finalOptions.cancelButtonColor,
      confirmButtonText: finalOptions.confirmButtonText,
      cancelButtonText: finalOptions.cancelButtonText
    })
  }
  
  /**
   * 顯示載入中提示
   * @param {string} message - 載入訊息
   */
  static showLoading(message = '處理中...') {
    Swal.fire({
      title: message,
      allowOutsideClick: false,
      allowEscapeKey: false,
      showConfirmButton: false,
      didOpen: () => {
        Swal.showLoading()
      }
    })
  }
  
  /**
   * 關閉載入中提示
   */
  static hideLoading() {
    Swal.close()
  }
}

export default ErrorHandler
