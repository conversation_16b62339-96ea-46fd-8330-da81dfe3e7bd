#!/usr/bin/env python3
"""
[?]
[?]
"""

import os
import sys
import importlib
from config import Config

def check_environment_variables():
    """[?]"""
    print("=== [?] ===")
    
    required_vars = [
        'PTT_USERNAME',
        'PTT_PASSWORD',
        'COSMOS_DB_ENDPOINT',
        'COSMOS_DB_KEY',
        'COSMOS_DB_DATABASE',
        'COSMOS_DB_COLLECTION'
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if not value or value == '[?]':
            missing_vars.append(var)
            print(f"[FAIL] {var}: [?]")
        else:
            # [?]
            if 'PASSWORD' in var or 'KEY' in var:
                display_value = value[:8] + '***' if len(value) > 8 else '***'
            else:
                display_value = value
            print(f"[OK] {var}: {display_value}")
    
    if missing_vars:
        print(f"\n[WARN] [?]: {', '.join(missing_vars)}")
        return False
    else:
        print("\n[OK] [?]")
        return True

def check_dependencies():
    """[?]"""
    print("\n=== [?] ===")
    
    required_packages = [
        'flask',
        'gunicorn',
        'PyPtt',
        'apscheduler',
        'python-dotenv',
        'gremlinpython',
        'pytz',
        'requests'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            # [?]
            import_name = package
            if package == 'python-dotenv':
                import_name = 'dotenv'
            elif package == 'gremlinpython':
                import_name = 'gremlin_python'
            elif package == 'PyPtt':
                import_name = 'PyPtt'
            
            importlib.import_module(import_name)
            print(f"[OK] {package}: [?]")
        except ImportError:
            missing_packages.append(package)
            print(f"[FAIL] {package}: [?]")
    
    if missing_packages:
        print(f"\n[WARN] [?]: {', '.join(missing_packages)}")
        print("[?]: pip install -r requirements.txt")
        return False
    else:
        print("\n[OK] [?]")
        return True

def check_configuration():
    """[?]"""
    print("\n=== [?] ===")
    
    try:
        # [?] PTT [?]
        ptt_errors = Config.validate_config()
        if ptt_errors:
            print("PTT [?]:")
            for error in ptt_errors:
                print(f"  [FAIL] {error}")
        else:
            print("[OK] PTT [?]")
        
        # [?] Cosmos DB [?]
        cosmos_errors = Config.validate_cosmos_config()
        if cosmos_errors:
            print("Cosmos DB [?]:")
            for error in cosmos_errors:
                print(f"  [FAIL] {error}")
        else:
            print("[OK] Cosmos DB [?]")
        
        return len(ptt_errors) == 0 and len(cosmos_errors) == 0
        
    except Exception as e:
        print(f"[FAIL] [?]: {e}")
        return False

def check_file_structure():
    """[?]"""
    print("\n=== [?] ===")
    
    required_files = [
        'app.py',
        'config.py',
        'logger.py',
        'ptt_client.py',
        'crawler.py',
        'graph_writer.py',
        'graph_query.py',
        'scheduler.py',
        'requirements.txt'
    ]
    
    required_dirs = [
        'templates',
        'static',
        'api'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"[OK] {file}")
        else:
            missing_files.append(file)
            print(f"[FAIL] {file}: [?]")
    
    for dir_name in required_dirs:
        if os.path.isdir(dir_name):
            print(f"[OK] {dir_name}/")
        else:
            missing_files.append(f"{dir_name}/")
            print(f"[FAIL] {dir_name}/: [?]")
    
    if missing_files:
        print(f"\n[WARN] [?]/[?]: {', '.join(missing_files)}")
        return False
    else:
        print("\n[OK] [?]")
        return True

def check_app_startup():
    """[?]"""
    print("\n=== [?] ===")
    
    try:
        from app import app
        print("[OK] Flask [?]")
        
        # [?]
        routes = []
        for rule in app.url_map.iter_rules():
            routes.append(rule.rule)
        
        expected_routes = [
            '/',
            '/user-analysis',
            '/ip-analysis',
            '/post-analysis',
            '/system-status',
            '/crawl-management',
            '/api/system/health',
            '/api/system/status'
        ]
        
        missing_routes = []
        for route in expected_routes:
            if route in routes:
                print(f"[OK] [?] {route}")
            else:
                missing_routes.append(route)
                print(f"[FAIL] [?] {route}: [?]")
        
        if missing_routes:
            print(f"\n[WARN] [?]: {', '.join(missing_routes)}")
            return False
        else:
            print("\n[OK] [?]")
            return True
        
    except Exception as e:
        print(f"[FAIL] [?]: {e}")
        return False

def generate_deployment_summary():
    """[?]"""
    print("\n=== [?] ===")
    
    config_summary = Config.get_config_summary()
    
    print("[?]:")
    for key, value in config_summary.items():
        print(f"  {key}: {value}")
    
    print(f"\n[?]: https://twlotterybot.azurewebsites.net")
    print(f"Python [?]: 3.13")
    print(f"[?]: gunicorn --bind=0.0.0.0 --timeout 600 app:app")

def main():
    """[?]"""
    print("PTT [?] - [?]")
    print("=" * 50)
    
    checks = [
        ("[?]", check_environment_variables),
        ("[?]", check_dependencies),
        ("[?]", check_configuration),
        ("[?]", check_file_structure),
        ("[?]", check_app_startup)
    ]
    
    passed = 0
    total = len(checks)
    
    for check_name, check_func in checks:
        try:
            if check_func():
                passed += 1
                print(f"\n[OK] {check_name} [?]")
            else:
                print(f"\n[FAIL] {check_name} [?]")
        except Exception as e:
            print(f"\n[FAIL] {check_name} [?]: {e}")
    
    print("\n" + "=" * 50)
    print(f"[?]: {passed}/{total} [?]")
    
    if passed == total:
        print("[SUCCESS] [?]")
        generate_deployment_summary()
        return 0
    else:
        print("[WARN] [?]")
        return 1

if __name__ == "__main__":
    exit(main())
