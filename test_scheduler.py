#!/usr/bin/env python3
"""
[?]
"""

import time
from datetime import datetime, timedelta
from scheduler import PTTScheduler
from config import Config

def test_scheduler_basic():
    """[?]"""
    print("=== [?] ===")
    
    try:
        # [?]
        scheduler = PTTScheduler()
        print("[OK] [?]")
        
        # [?]
        print("1. [?]...")
        if scheduler.start():
            print("[OK] [?]")
        else:
            print("[FAIL] [?]")
            return False
        
        # [?]
        print("2. [?]...")
        status = scheduler.get_status()
        print(f"   [?]: {'[?]' if status['is_running'] else '[?]'}")
        print(f"   [?]: {len(status['jobs'])}")
        
        if status['jobs']:
            print("   [?]:")
            for job in status['jobs']:
                print(f"     - {job['name']}: [?] {job['next_run']}")
        
        # [?]
        print("3. [?]...")
        if scheduler.stop():
            print("[OK] [?]")
        else:
            print("[FAIL] [?]")
            return False
        
        return True
        
    except Exception as e:
        print(f"[FAIL] [?]: {e}")
        return False

def test_schedule_tasks():
    """[?]"""
    print("\n=== [?] ===")
    
    try:
        scheduler = PTTScheduler()
        
        # [?]
        if not scheduler.start():
            print("[FAIL] [?]")
            return False
        
        print("1. [?]...")
        # [?]1[?]
        now = datetime.now()
        test_hour = now.hour
        test_minute = (now.minute + 1) % 60
        
        if scheduler.schedule_daily_crawl(test_hour, test_minute):
            print(f"[OK] [?]: {test_hour:02d}:{test_minute:02d}")
        else:
            print("[FAIL] [?]")
        
        print("2. [?]...")
        if scheduler.schedule_interval_crawl(1):  # [?]1[?]
            print("[OK] [?]: [?]1[?]")
        else:
            print("[FAIL] [?]")
        
        # [?]
        print("3. [?]...")
        status = scheduler.get_status()
        print(f"   [?]: {len(status['jobs'])}")
        
        for job in status['jobs']:
            print(f"   - {job['name']}")
            print(f"     [?]: {job['next_run']}")
            print(f"     [?]: {job['trigger']}")
        
        # [?]
        next_time = scheduler.get_next_crawl_time()
        if next_time:
            print(f"   [?]: {next_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # [?]
        scheduler.stop()
        return True
        
    except Exception as e:
        print(f"[FAIL] [?]: {e}")
        return False

def test_manual_crawl():
    """[?]"""
    print("\n=== [?] ===")
    
    try:
        scheduler = PTTScheduler()
        
        # [?] PTT [?]
        config_errors = Config.validate_config()
        if config_errors:
            print("[WARN] PTT [?]")
            for error in config_errors:
                print(f"     - {error}")
            return True  # [?]
        
        print("1. [?]...")
        print("   ([?])")
        
        # [?]
        result = scheduler.execute_manual_crawl(['Test'], 2)
        
        if result['success']:
            print("[OK] [?]")
            print(f"   [?]: {result['summary']['boards_count']}")
            print(f"   [?]: {result['summary']['total_posts']}")
            print(f"   [?]: {result['summary']['total_users']}")
            print(f"   [?] IP [?]: {result['summary']['total_ips']}")
        else:
            print(f"[FAIL] [?]: {result.get('error', '[?]')}")
            return False
        
        return True
        
    except Exception as e:
        print(f"[FAIL] [?]: {e}")
        return False

def test_scheduler_integration():
    """[?]"""
    print("\n=== [?] ===")
    
    try:
        scheduler = PTTScheduler()
        
        # [?]
        scheduler.start()
        
        print("1. [?]...")
        status = scheduler.get_status()
        
        print(f"   [?]: {status['is_running']}")
        print(f"   [?]: {status['last_crawl_time'] or '[?]'}")
        print(f"   [?]: {status['total_results']}")
        print(f"   [?]: {len(status['recent_results'])}")
        
        print("2. [?]...")
        print(f"   [?]: {Config.DEFAULT_BOARDS}")
        print(f"   [?]: {Config.MAX_POSTS_PER_BOARD}")
        print(f"   [?]: {Config.CRAWL_INTERVAL_HOURS} [?]")
        
        # [?]
        scheduler.stop()
        
        return True
        
    except Exception as e:
        print(f"[FAIL] [?]: {e}")
        return False

def test_error_handling():
    """[?]"""
    print("\n=== [?] ===")
    
    try:
        scheduler = PTTScheduler()
        
        print("1. [?]...")
        scheduler.start()
        
        # [?]
        if not scheduler.start():
            print("[OK] [?]")
        else:
            print("[?] [?]")
        
        print("2. [?]...")
        scheduler.stop()
        
        # [?]
        if not scheduler.stop():
            print("[OK] [?]")
        else:
            print("[?] [?]")
        
        print("3. [?]...")
        scheduler.start()
        
        # [?]
        try:
            scheduler.schedule_daily_crawl(25, 70)  # [?]
            print("[?] [?]")
        except:
            print("[OK] [?]")
        
        scheduler.stop()
        return True
        
    except Exception as e:
        print(f"[FAIL] [?]: {e}")
        return False

def main():
    """[?]"""
    print("[?]")
    print("=" * 50)
    
    # [?]
    config_summary = Config.get_config_summary()
    print("[?]:")
    print(f"  PTT [?]: {config_summary['PTT_USERNAME']}")
    print(f"  [?]: {config_summary['DEFAULT_BOARDS']}")
    print(f"  [?]: {config_summary['CRAWL_INTERVAL_HOURS']} [?]")
    print()
    
    # [?]
    tests = [
        ("[?]", test_scheduler_basic),
        ("[?]", test_schedule_tasks),
        ("[?]", test_manual_crawl),
        ("[?]", test_scheduler_integration),
        ("[?]", test_error_handling),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"[OK] {test_name} [?]")
            else:
                print(f"[FAIL] {test_name} [?]")
        except Exception as e:
            print(f"[FAIL] {test_name} [?]: {e}")
    
    print("\n" + "=" * 50)
    print(f"[?]: {passed}/{total} [?]")
    
    if passed == total:
        print("[SUCCESS] [?]")
        return 0
    else:
        print("[?] [?]")
        return 1

if __name__ == "__main__":
    exit(main())
