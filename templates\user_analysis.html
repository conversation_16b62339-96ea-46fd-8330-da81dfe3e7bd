{% extends "base.html" %}

{% block title %}使用者關聯分析 - PTT 圖形分析系統{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="bi bi-person-lines-fill"></i> 使用者關聯分析
        </h1>
        <p class="lead">分析 PTT 使用者之間的互動關係，包括共用 IP、相同看板發文等關聯。</p>
    </div>
</div>

<!-- 搜尋表單 -->
<div class="search-form">
    <form id="userSearchForm">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="username" class="form-label">使用者名稱</label>
                    <input type="text" class="form-control" id="username" name="username" 
                           placeholder="輸入要分析的使用者名稱" required>
                </div>
            </div>
            <div class="col-md-3">
                <div class="mb-3">
                    <label for="maxDepth" class="form-label">查詢深度</label>
                    <select class="form-select" id="maxDepth" name="maxDepth">
                        <option value="1">1 層</option>
                        <option value="2" selected>2 層</option>
                        <option value="3">3 層</option>
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="mb-3">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search"></i> 分析關聯
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="clearResults()">
                            <i class="bi bi-arrow-clockwise"></i> 清除
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- 載入狀態 -->
<div id="loadingSpinner" class="loading-spinner">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">載入中...</span>
    </div>
    <p class="mt-2">正在分析使用者關聯...</p>
</div>

<!-- 結果區域 -->
<div id="resultsContainer" style="display: none;">
    <!-- 統計摘要 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="stats-number" id="postsCount">0</div>
                    <div class="text-muted">發文數</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="stats-number" id="commentsCount">0</div>
                    <div class="text-muted">推文數</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="stats-number" id="ipsCount">0</div>
                    <div class="text-muted">使用 IP 數</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="stats-number" id="connectionsCount">0</div>
                    <div class="text-muted">關聯使用者</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 關聯圖形 -->
    <div class="card result-card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-diagram-3"></i> 使用者關聯圖
            </h5>
            <div class="graph-controls">
                <button class="btn btn-sm btn-outline-primary" onclick="fitGraph()">
                    <i class="bi bi-arrows-fullscreen"></i> 適應視窗
                </button>
                <button class="btn btn-sm btn-outline-secondary" onclick="resetGraph()">
                    <i class="bi bi-arrow-clockwise"></i> 重置佈局
                </button>
                <button class="btn btn-sm btn-outline-info" onclick="exportGraph()">
                    <i class="bi bi-download"></i> 匯出圖片
                </button>
            </div>
        </div>
        <div class="card-body">
            <div id="userGraph" class="graph-container"></div>
            
            <!-- 圖例 -->
            <div class="graph-legend">
                <h6>圖例</h6>
                <div class="legend-item">
                    <span class="legend-color user-node"></span>
                    <span>使用者</span>
                </div>
                <div class="legend-item">
                    <span class="legend-color ip-node"></span>
                    <span>IP 位址</span>
                </div>
                <div class="legend-item">
                    <span class="legend-color post-node"></span>
                    <span>文章</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 詳細資訊 -->
    <div class="row">
        <!-- 發文列表 -->
        <div class="col-md-6">
            <div class="card result-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-file-text"></i> 發文記錄
                    </h5>
                </div>
                <div class="card-body">
                    <div id="postsList" class="list-group list-group-flush"></div>
                </div>
            </div>
        </div>

        <!-- 推文列表 -->
        <div class="col-md-6">
            <div class="card result-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-chat-dots"></i> 推文記錄
                    </h5>
                </div>
                <div class="card-body">
                    <div id="commentsList" class="list-group list-group-flush"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 關聯使用者 -->
    <div class="card result-card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-people"></i> 關聯使用者
            </h5>
        </div>
        <div class="card-body">
            <div id="connectionsList" class="row"></div>
        </div>
    </div>
</div>

<!-- 錯誤訊息 -->
<div id="errorContainer"></div>
{% endblock %}

{% block extra_js %}
<script>
let userGraph = null;

// 表單提交處理
document.getElementById('userSearchForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const username = document.getElementById('username').value.trim();
    const maxDepth = document.getElementById('maxDepth').value;
    
    if (!username) {
        showError('請輸入使用者名稱', 'errorContainer');
        return;
    }
    
    // 顯示載入狀態
    showLoading('loadingSpinner');
    document.getElementById('resultsContainer').style.display = 'none';
    document.getElementById('errorContainer').innerHTML = '';
    
    try {
        // 呼叫 API
        const response = await apiRequest(`/api/user-links?username=${encodeURIComponent(username)}&max_depth=${maxDepth}`);
        
        if (response.success) {
            displayUserAnalysis(response.data);
        } else {
            showError(response.error || '查詢失敗', 'errorContainer');
        }
    } catch (error) {
        showError(`查詢失敗: ${error.message}`, 'errorContainer');
    } finally {
        hideLoading('loadingSpinner');
    }
});

// 顯示使用者分析結果
function displayUserAnalysis(data) {
    // 更新統計數字
    document.getElementById('postsCount').textContent = data.posts ? data.posts.length : 0;
    document.getElementById('commentsCount').textContent = data.comments ? data.comments.length : 0;
    document.getElementById('ipsCount').textContent = data.ips ? data.ips.length : 0;
    document.getElementById('connectionsCount').textContent = data.connections ? data.connections.length : 0;
    
    // 建立關聯圖
    createUserGraph(data);
    
    // 顯示發文列表
    displayPostsList(data.posts || []);
    
    // 顯示推文列表
    displayCommentsList(data.comments || []);
    
    // 顯示關聯使用者
    displayConnectionsList(data.connections || []);
    
    // 顯示結果區域
    document.getElementById('resultsContainer').style.display = 'block';
}

// 建立使用者關聯圖
function createUserGraph(data) {
    const container = document.getElementById('userGraph');
    
    // 準備圖形資料
    const elements = [];
    
    // 添加中心使用者節點
    elements.push({
        data: { 
            id: data.user, 
            label: data.user,
            type: 'user',
            isCenter: true
        }
    });
    
    // 添加 IP 節點
    if (data.ips) {
        data.ips.forEach(ip => {
            elements.push({
                data: { 
                    id: `ip_${ip}`, 
                    label: ip,
                    type: 'ip'
                }
            });
            
            // 添加使用者到 IP 的邊
            elements.push({
                data: { 
                    source: data.user, 
                    target: `ip_${ip}`,
                    type: 'used_ip'
                }
            });
        });
    }
    
    // 添加關聯使用者節點和邊
    if (data.connections) {
        data.connections.forEach(conn => {
            elements.push({
                data: { 
                    id: conn.target_user, 
                    label: conn.target_user,
                    type: 'user'
                }
            });
            
            elements.push({
                data: { 
                    source: data.user, 
                    target: conn.target_user,
                    type: conn.type,
                    label: conn.type
                }
            });
        });
    }
    
    // 建立 Cytoscape 圖形
    userGraph = cytoscape({
        container: container,
        elements: elements,
        style: [
            {
                selector: 'node',
                style: {
                    'background-color': '#007bff',
                    'label': 'data(label)',
                    'text-valign': 'center',
                    'text-halign': 'center',
                    'color': 'white',
                    'text-outline-width': 2,
                    'text-outline-color': '#000',
                    'font-size': '12px',
                    'width': 60,
                    'height': 60
                }
            },
            {
                selector: 'node[type="user"]',
                style: {
                    'background-color': '#007bff'
                }
            },
            {
                selector: 'node[type="ip"]',
                style: {
                    'background-color': '#28a745',
                    'shape': 'rectangle'
                }
            },
            {
                selector: 'node[isCenter]',
                style: {
                    'background-color': '#dc3545',
                    'width': 80,
                    'height': 80,
                    'font-size': '14px',
                    'font-weight': 'bold'
                }
            },
            {
                selector: 'edge',
                style: {
                    'width': 2,
                    'line-color': '#ccc',
                    'target-arrow-color': '#ccc',
                    'target-arrow-shape': 'triangle',
                    'curve-style': 'bezier'
                }
            },
            {
                selector: 'edge[type="used_ip"]',
                style: {
                    'line-color': '#28a745',
                    'target-arrow-color': '#28a745'
                }
            }
        ],
        layout: {
            name: 'cose',  // 使用內建的 cose 佈局
            animate: true,
            animationDuration: 1000,
            nodeDimensionsIncludeLabels: true,
            idealEdgeLength: 100,
            nodeOverlap: 20,
            refresh: 20,
            fit: true,
            padding: 30,
            randomize: false,
            componentSpacing: 100,
            nodeRepulsion: 400000,
            edgeElasticity: 100,
            nestingFactor: 5,
            gravity: 80,
            numIter: 1000,
            initialTemp: 200,
            coolingFactor: 0.95,
            minTemp: 1.0
        }
    });
    
    // 添加點擊事件
    userGraph.on('tap', 'node', function(evt) {
        const node = evt.target;
        const nodeData = node.data();
        
        // 顯示節點詳細資訊
        alert(`節點資訊:\n類型: ${nodeData.type}\n標籤: ${nodeData.label}`);
    });
}

// 顯示發文列表
function displayPostsList(posts) {
    const container = document.getElementById('postsList');
    
    if (posts.length === 0) {
        container.innerHTML = '<div class="text-muted">無發文記錄</div>';
        return;
    }
    
    let html = '';
    posts.slice(0, 10).forEach(post => {  // 只顯示前10篇
        html += `
            <div class="list-group-item">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">${post.title || '無標題'}</h6>
                    <small>${post.date || ''}</small>
                </div>
                <p class="mb-1">看板: ${post.board || '未知'}</p>
                <small>文章ID: ${post.post_id || '未知'}</small>
            </div>
        `;
    });
    
    if (posts.length > 10) {
        html += `<div class="list-group-item text-muted">還有 ${posts.length - 10} 篇文章...</div>`;
    }
    
    container.innerHTML = html;
}

// 顯示推文列表
function displayCommentsList(comments) {
    const container = document.getElementById('commentsList');
    
    if (comments.length === 0) {
        container.innerHTML = '<div class="text-muted">無推文記錄</div>';
        return;
    }
    
    let html = '';
    comments.slice(0, 10).forEach(comment => {  // 只顯示前10則
        const commentData = comment.comment || {};
        const postData = comment.post || {};
        
        html += `
            <div class="list-group-item">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">[${commentData.comment_type || '?'}] ${commentData.content || '無內容'}</h6>
                    <small>${commentData.time || ''}</small>
                </div>
                <p class="mb-1">文章: ${postData.title || '未知'}</p>
                <small>看板: ${postData.board || '未知'}</small>
            </div>
        `;
    });
    
    if (comments.length > 10) {
        html += `<div class="list-group-item text-muted">還有 ${comments.length - 10} 則推文...</div>`;
    }
    
    container.innerHTML = html;
}

// 顯示關聯使用者
function displayConnectionsList(connections) {
    const container = document.getElementById('connectionsList');
    
    if (connections.length === 0) {
        container.innerHTML = '<div class="col-12"><div class="text-muted">無關聯使用者</div></div>';
        return;
    }
    
    let html = '';
    connections.forEach(conn => {
        html += `
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="bi bi-person"></i> ${conn.target_user}
                        </h6>
                        <p class="card-text">
                            <span class="badge bg-primary">${conn.type}</span>
                        </p>
                        <button class="btn btn-sm btn-outline-primary" onclick="analyzeUser('${conn.target_user}')">
                            分析此使用者
                        </button>
                    </div>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// 圖形控制函數
function fitGraph() {
    if (userGraph) {
        userGraph.fit();
    }
}

function resetGraph() {
    if (userGraph) {
        userGraph.layout({
            name: 'cose',
            animate: true,
            fit: true,
            padding: 30
        }).run();
    }
}

function exportGraph() {
    if (userGraph) {
        const png64 = userGraph.png({ scale: 2 });
        const link = document.createElement('a');
        link.href = png64;
        link.download = `user_analysis_${document.getElementById('username').value}.png`;
        link.click();
    }
}

// 分析其他使用者
function analyzeUser(username) {
    document.getElementById('username').value = username;
    document.getElementById('userSearchForm').dispatchEvent(new Event('submit'));
}

// 清除結果
function clearResults() {
    document.getElementById('resultsContainer').style.display = 'none';
    document.getElementById('errorContainer').innerHTML = '';
    document.getElementById('username').value = '';
    if (userGraph) {
        userGraph.destroy();
        userGraph = null;
    }
}
</script>
{% endblock %}
