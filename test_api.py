#!/usr/bin/env python3
"""
[?] Graph API [?]
"""

import requests
import json
import time
from app import app

def test_api_basic():
    """[?] API [?]"""
    print("=== [?] API [?] ===")
    
    try:
        # [?]
        with app.test_client() as client:
            print("[OK] Flask [?]")
            
            # [?]
            print("1. [?]...")
            response = client.get('/api/system/health')
            
            if response.status_code == 200:
                data = response.get_json()
                print(f"[OK] [?]: {data['data']['status']}")
            else:
                print(f"[FAIL] [?]: {response.status_code}")
                return False
            
            # [?]
            print("2. [?]...")
            response = client.get('/api/system/status')
            
            if response.status_code == 200:
                data = response.get_json()
                print("[OK] [?]")
                print(f"   PTT [?]: {'[?]' if data['data']['configuration']['ptt_configured'] else '[?]'}")
                print(f"   Cosmos DB [?]: {'[?]' if data['data']['configuration']['cosmos_configured'] else '[?]'}")
                print(f"   [?]: {data['data']['services']['graph_query']['status']}")
                print(f"   [?]: {data['data']['services']['graph_writer']['status']}")
            else:
                print(f"[FAIL] [?]: {response.status_code}")
                return False
            
            # [?]
            print("3. [?]...")
            response = client.get('/api/system/config')
            
            if response.status_code == 200:
                data = response.get_json()
                print("[OK] [?]")
                config = data['data']['configuration']
                print(f"   [?]: {config['DEFAULT_BOARDS']}")
                print(f"   [?]: {config['MAX_POSTS_PER_BOARD']}")
                print(f"   [?]: {config['CRAWL_INTERVAL_HOURS']} [?]")
            else:
                print(f"[FAIL] [?]: {response.status_code}")
                return False
        
        return True
        
    except Exception as e:
        print(f"[FAIL] [?]: {e}")
        return False

def test_graph_api_endpoints():
    """[?] API [?]"""
    print("\n=== [?] API [?] ===")
    
    try:
        with app.test_client() as client:
            
            # [?]
            print("1. [?]...")
            response = client.get('/api/user-links?username=test_user')
            
            if response.status_code in [200, 500]:  # [?]
                data = response.get_json()
                if data['success']:
                    print("[OK] [?]")
                else:
                    print(f"[?] [?]: {data['error']}")
            else:
                print(f"[FAIL] [?]: {response.status_code}")
            
            # [?]
            print("2. [?]...")
            response = client.get('/api/user-links')
            
            if response.status_code == 400:
                data = response.get_json()
                print(f"[OK] [?]: {data['error']}")
            else:
                print(f"[FAIL] [?]: {response.status_code}")
            
            # [?] IP [?]
            print("3. [?] IP [?]...")
            response = client.get('/api/ip-users?ip=***********')
            
            if response.status_code in [200, 500]:
                data = response.get_json()
                if data['success']:
                    print("[OK] IP [?]")
                else:
                    print(f"[?] IP [?]: {data['error']}")
            else:
                print(f"[FAIL] IP [?]: {response.status_code}")
            
            # [?] IP [?]
            print("4. [?] IP [?]...")
            response = client.get('/api/ip-users?ip=invalid_ip')
            
            if response.status_code == 400:
                data = response.get_json()
                print(f"[OK] [?] IP: {data['error']}")
            else:
                print(f"[FAIL] [?] IP: {response.status_code}")
            
            # [?]
            print("5. [?]...")
            response = client.get('/api/post-interactions?post_id=Test.M.123456')
            
            if response.status_code in [200, 500]:
                data = response.get_json()
                if data['success']:
                    print("[OK] [?]")
                else:
                    print(f"[?] [?]: {data['error']}")
            else:
                print(f"[FAIL] [?]: {response.status_code}")
            
            # [?]
            print("6. [?]...")
            response = client.get('/api/search-users?pattern=test&limit=5')
            
            if response.status_code == 200:
                data = response.get_json()
                print(f"[OK] [?] {data['data']['count']} [?]")
            else:
                print(f"[FAIL] [?]: {response.status_code}")
            
            # [?]
            print("7. [?]...")
            response = client.get('/api/popular-boards?limit=5')
            
            if response.status_code == 200:
                data = response.get_json()
                print(f"[OK] [?] {data['data']['count']} [?]")
            else:
                print(f"[FAIL] [?]: {response.status_code}")
            
            # [?]
            print("8. [?]...")
            response = client.get('/api/graph-stats')
            
            if response.status_code in [200, 500]:
                data = response.get_json()
                if data['success']:
                    print("[OK] [?]")
                else:
                    print(f"[?] [?]: {data['error']}")
            else:
                print(f"[FAIL] [?]: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] [?]: {e}")
        return False

def test_system_api_endpoints():
    """[?] API [?]"""
    print("\n=== [?] API [?] ===")
    
    try:
        with app.test_client() as client:
            
            # [?]
            print("1. [?]...")
            response = client.get('/api/system/logs?lines=10')
            
            if response.status_code == 200:
                data = response.get_json()
                print(f"[OK] [?] {data['data']['total_lines']} [?]")
            else:
                print(f"[FAIL] [?]: {response.status_code}")
            
            # [?] PTT [?]
            print("2. [?]...")
            crawl_data = {
                "boards": ["Test"],
                "max_posts_per_board": 2
            }
            
            response = client.post('/api/system/crawl', 
                                 data=json.dumps(crawl_data),
                                 content_type='application/json')
            
            if response.status_code in [200, 400]:  # [?]
                data = response.get_json()
                if data['success']:
                    print("[OK] [?]")
                    print(f"   [?]: {data['data']['summary']['boards_count']}")
                    print(f"   [?]: {data['data']['summary']['total_posts']}")
                else:
                    print(f"[?] [?]: {data['error']}")
            else:
                print(f"[FAIL] [?]: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] [?]: {e}")
        return False

def test_cors_headers():
    """[?] CORS [?]"""
    print("\n=== [?] CORS [?] ===")
    
    try:
        with app.test_client() as client:
            response = client.get('/api/system/health')
            
            # [?] CORS [?]
            cors_headers = [
                'Access-Control-Allow-Origin',
                'Access-Control-Allow-Headers',
                'Access-Control-Allow-Methods'
            ]
            
            for header in cors_headers:
                if header in response.headers:
                    print(f"[OK] {header}: {response.headers[header]}")
                else:
                    print(f"[FAIL] [?] CORS [?]: {header}")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] [?]: {e}")
        return False

def main():
    """[?]"""
    print("Graph API [?]")
    print("=" * 50)
    
    # [?]
    tests = [
        ("API [?]", test_api_basic),
        ("[?] API", test_graph_api_endpoints),
        ("[?] API", test_system_api_endpoints),
        ("CORS [?]", test_cors_headers),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"[OK] {test_name} [?]")
            else:
                print(f"[FAIL] {test_name} [?]")
        except Exception as e:
            print(f"[FAIL] {test_name} [?]: {e}")
    
    print("\n" + "=" * 50)
    print(f"[?]: {passed}/{total} [?]")
    
    if passed == total:
        print("[SUCCESS] [?] API [?]")
        return 0
    else:
        print("[?] [?] API [?]")
        return 1

if __name__ == "__main__":
    exit(main())
