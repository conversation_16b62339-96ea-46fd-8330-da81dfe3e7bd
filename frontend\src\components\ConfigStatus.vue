<template>
  <div class="config-status">
    <div class="card">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
          <i class="fas fa-cog me-2"></i>系統配置狀態
        </h5>
        <button 
          class="btn btn-outline-primary btn-sm" 
          @click="refreshConfig"
          :disabled="loading"
        >
          <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
          重新檢查
        </button>
      </div>
      
      <div class="card-body">
        <!-- 載入中 -->
        <div v-if="loading" class="text-center py-4">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">載入中...</span>
          </div>
          <p class="mt-2 text-muted">檢查配置中...</p>
        </div>
        
        <!-- 配置狀態 -->
        <div v-else>
          <!-- 整體健康度 -->
          <div class="row mb-4">
            <div class="col-md-6">
              <div class="d-flex align-items-center">
                <div class="health-indicator me-3">
                  <div 
                    class="progress" 
                    style="width: 100px; height: 100px; border-radius: 50%;"
                  >
                    <div 
                      class="progress-bar" 
                      :class="getHealthClass(healthPercentage)"
                      :style="{ 
                        width: healthPercentage + '%',
                        borderRadius: '50%'
                      }"
                    ></div>
                  </div>
                  <div class="text-center mt-2">
                    <strong>{{ healthPercentage.toFixed(1) }}%</strong>
                    <br>
                    <small class="text-muted">系統健康度</small>
                  </div>
                </div>
                <div>
                  <h6 class="mb-1">
                    <i 
                      class="fas me-2" 
                      :class="getStatusIcon(systemStatus)"
                    ></i>
                    {{ getStatusText(systemStatus) }}
                  </h6>
                  <p class="text-muted mb-0">
                    {{ getStatusDescription(systemStatus) }}
                  </p>
                </div>
              </div>
            </div>
          </div>
          
          <!-- PTT 配置 -->
          <div class="config-section mb-4">
            <h6 class="d-flex align-items-center mb-3">
              <i 
                class="fas fa-circle me-2" 
                :class="pttConfig.is_valid ? 'text-success' : 'text-danger'"
              ></i>
              PTT 配置
              <span 
                class="badge ms-2" 
                :class="pttConfig.is_valid ? 'bg-success' : 'bg-danger'"
              >
                {{ pttConfig.is_valid ? '正常' : '有問題' }}
              </span>
            </h6>
            
            <!-- PTT 錯誤 -->
            <div v-if="pttConfig.errors.length > 0" class="mb-3">
              <div 
                v-for="error in pttConfig.errors" 
                :key="error.field"
                class="alert alert-danger d-flex align-items-start"
              >
                <i class="fas fa-exclamation-triangle me-2 mt-1"></i>
                <div class="flex-grow-1">
                  <strong>{{ error.field }}:</strong> {{ error.message }}
                  <br>
                  <small class="text-muted">{{ error.suggestion }}</small>
                </div>
              </div>
            </div>
            
            <!-- PTT 警告 -->
            <div v-if="pttConfig.warnings.length > 0" class="mb-3">
              <div 
                v-for="warning in pttConfig.warnings" 
                :key="warning.field"
                class="alert alert-warning d-flex align-items-start"
              >
                <i class="fas fa-exclamation-circle me-2 mt-1"></i>
                <div class="flex-grow-1">
                  <strong>{{ warning.field }}:</strong> {{ warning.message }}
                  <br>
                  <small class="text-muted">{{ warning.suggestion }}</small>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Cosmos DB 配置 -->
          <div class="config-section mb-4">
            <h6 class="d-flex align-items-center mb-3">
              <i 
                class="fas fa-circle me-2" 
                :class="cosmosConfig.is_valid ? 'text-success' : 'text-danger'"
              ></i>
              Cosmos DB 配置
              <span 
                class="badge ms-2" 
                :class="cosmosConfig.is_valid ? 'bg-success' : 'bg-danger'"
              >
                {{ cosmosConfig.is_valid ? '正常' : '有問題' }}
              </span>
            </h6>
            
            <!-- Cosmos 錯誤 -->
            <div v-if="cosmosConfig.errors.length > 0" class="mb-3">
              <div 
                v-for="error in cosmosConfig.errors" 
                :key="error.field"
                class="alert alert-danger d-flex align-items-start"
              >
                <i class="fas fa-exclamation-triangle me-2 mt-1"></i>
                <div class="flex-grow-1">
                  <strong>{{ error.field }}:</strong> {{ error.message }}
                  <br>
                  <small class="text-muted">{{ error.suggestion }}</small>
                </div>
              </div>
            </div>
            
            <!-- Cosmos 警告 -->
            <div v-if="cosmosConfig.warnings.length > 0" class="mb-3">
              <div 
                v-for="warning in cosmosConfig.warnings" 
                :key="warning.field"
                class="alert alert-warning d-flex align-items-start"
              >
                <i class="fas fa-exclamation-circle me-2 mt-1"></i>
                <div class="flex-grow-1">
                  <strong>{{ warning.field }}:</strong> {{ warning.message }}
                  <br>
                  <small class="text-muted">{{ warning.suggestion }}</small>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 服務狀態 -->
          <div class="config-section">
            <h6 class="mb-3">
              <i class="fas fa-server me-2"></i>服務狀態
            </h6>
            
            <div class="row">
              <div class="col-md-6 mb-3">
                <div class="service-status">
                  <div class="d-flex align-items-center">
                    <i 
                      class="fas fa-circle me-2" 
                      :class="services.graph_query.available ? 'text-success' : 'text-danger'"
                    ></i>
                    <div>
                      <strong>圖形查詢引擎</strong>
                      <br>
                      <small class="text-muted">{{ services.graph_query.status }}</small>
                      <div v-if="services.graph_query.error" class="text-danger small">
                        {{ services.graph_query.error }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="col-md-6 mb-3">
                <div class="service-status">
                  <div class="d-flex align-items-center">
                    <i 
                      class="fas fa-circle me-2" 
                      :class="services.graph_writer.available ? 'text-success' : 'text-danger'"
                    ></i>
                    <div>
                      <strong>圖形寫入器</strong>
                      <br>
                      <small class="text-muted">{{ services.graph_writer.status }}</small>
                      <div v-if="services.graph_writer.error" class="text-danger small">
                        {{ services.graph_writer.error }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { API } from '../utils/apiClient.js'
import ErrorHandler from '../utils/errorHandler.js'

// 響應式資料
const loading = ref(false)
const healthPercentage = ref(0)
const systemStatus = ref('unknown')
const pttConfig = ref({ errors: [], warnings: [], is_valid: false })
const cosmosConfig = ref({ errors: [], warnings: [], is_valid: false })
const services = ref({
  graph_query: { available: false, status: '未知', error: null },
  graph_writer: { available: false, status: '未知', error: null }
})

// 載入配置狀態
const loadConfigStatus = async () => {
  loading.value = true
  
  try {
    const result = await API.system.getStatus()
    
    if (result.success) {
      const data = result.data
      
      healthPercentage.value = data.system.health_percentage
      systemStatus.value = data.system.status
      pttConfig.value = data.configuration.ptt_config
      cosmosConfig.value = data.configuration.cosmos_config
      services.value = data.services
    }
  } catch (error) {
    ErrorHandler.showWarning('無法載入配置狀態')
  } finally {
    loading.value = false
  }
}

// 重新檢查配置
const refreshConfig = () => {
  loadConfigStatus()
}

// 取得健康度樣式
const getHealthClass = (percentage) => {
  if (percentage >= 75) return 'bg-success'
  if (percentage >= 50) return 'bg-warning'
  return 'bg-danger'
}

// 取得狀態圖示
const getStatusIcon = (status) => {
  switch (status) {
    case 'healthy': return 'fa-check-circle text-success'
    case 'degraded': return 'fa-exclamation-triangle text-warning'
    case 'unhealthy': return 'fa-times-circle text-danger'
    default: return 'fa-question-circle text-muted'
  }
}

// 取得狀態文字
const getStatusText = (status) => {
  switch (status) {
    case 'healthy': return '系統正常'
    case 'degraded': return '系統降級'
    case 'unhealthy': return '系統異常'
    default: return '狀態未知'
  }
}

// 取得狀態描述
const getStatusDescription = (status) => {
  switch (status) {
    case 'healthy': return '所有服務運行正常'
    case 'degraded': return '部分服務可能受影響'
    case 'unhealthy': return '多個服務無法正常運行'
    default: return '無法確定系統狀態'
  }
}

// 組件掛載時載入資料
onMounted(() => {
  loadConfigStatus()
})
</script>

<style scoped>
.config-status {
  max-width: 800px;
  margin: 0 auto;
}

.health-indicator {
  position: relative;
}

.health-indicator .progress {
  background-color: #e9ecef;
  position: relative;
}

.health-indicator .progress-bar {
  transition: width 0.6s ease;
}

.config-section {
  border-left: 3px solid #dee2e6;
  padding-left: 1rem;
}

.service-status {
  padding: 0.75rem;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  background-color: #f8f9fa;
}

.alert {
  border-radius: 0.375rem;
  border: none;
}

.badge {
  font-size: 0.75em;
}
</style>
