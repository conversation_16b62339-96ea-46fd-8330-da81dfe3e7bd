"""
PTT 自動登入系統 - 日誌記錄模組
"""
import logging
import os
from datetime import datetime
from config import Config

class PTTLogger:
    """PTT 系統專用日誌記錄器"""
    
    def __init__(self, name='PTTSystem'):
        self.logger = logging.getLogger(name)
        self.setup_logger()
    
    def setup_logger(self):
        """設定日誌記錄器"""
        # 設定日誌等級
        level = getattr(logging, Config.LOG_LEVEL.upper(), logging.INFO)
        self.logger.setLevel(level)
        
        # 避免重複添加處理器
        if self.logger.handlers:
            return
        
        # 建立日誌格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 控制台處理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # 檔案處理器
        try:
            # 確保日誌目錄存在
            log_dir = 'logs'
            if not os.path.exists(log_dir):
                os.makedirs(log_dir)
            
            # 建立日誌檔案（按日期命名）
            log_filename = f"{log_dir}/ptt_system_{datetime.now().strftime('%Y%m%d')}.log"
            file_handler = logging.FileHandler(log_filename, encoding='utf-8')
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
        except Exception as e:
            self.logger.warning(f"無法建立檔案日誌處理器: {e}")
    
    def info(self, message):
        """記錄資訊訊息"""
        self.logger.info(message)
    
    def warning(self, message):
        """記錄警告訊息"""
        self.logger.warning(message)
    
    def error(self, message):
        """記錄錯誤訊息"""
        self.logger.error(message)
    
    def debug(self, message):
        """記錄除錯訊息"""
        self.logger.debug(message)
    
    def log_operation_start(self, operation):
        """記錄操作開始"""
        self.info(f"=== 開始執行: {operation} ===")
    
    def log_operation_end(self, operation, success=True, message=""):
        """記錄操作結束"""
        status = "成功" if success else "失敗"
        self.info(f"=== 完成執行: {operation} - {status} {message} ===")
    
    def log_login_attempt(self, username):
        """記錄登入嘗試"""
        self.info(f"嘗試登入 PTT - 使用者: {username}")
    
    def log_login_success(self, username):
        """記錄登入成功"""
        self.info(f"PTT 登入成功 - 使用者: {username}")
    
    def log_login_failure(self, username, error):
        """記錄登入失敗"""
        self.error(f"PTT 登入失敗 - 使用者: {username}, 錯誤: {error}")
    
    def log_connection_error(self, error):
        """記錄連接錯誤"""
        self.error(f"PTT 連接錯誤: {error}")
    
    def log_retry_attempt(self, attempt, max_attempts):
        """記錄重試嘗試"""
        self.warning(f"重試第 {attempt}/{max_attempts} 次")

    def get_timestamp(self):
        """取得當前時間戳記"""
        return datetime.now().strftime('%Y-%m-%d %H:%M:%S')

# 建立全域日誌記錄器實例
ptt_logger = PTTLogger()
